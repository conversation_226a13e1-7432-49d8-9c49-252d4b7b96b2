# 修复延迟显示和添加语音延迟追踪

## 任务背景

- 心跳响应显示延迟为 NaNms
- 需要添加语音回复延迟显示功能

## 实施计划

### 1. 修复心跳响应延迟显示

- 在 WebSocketTester 类中添加 pingTimestamp 属性
- sendPing 时记录时间戳
- 收到 pong 时计算延迟

### 2. 添加语音回复延迟追踪

- 添加 voiceRequestTimestamp 属性记录请求时间
- 添加 voiceResponseHistory 数组存储历史
- 在 ai_start 时计算文本延迟
- 在首次 sentence_audio_chunk 时计算音频延迟

### 3. UI 显示增强

- 在状态信息区添加语音延迟显示
- 显示当前和平均延迟
- 实时更新显示

## 预期结果

- 心跳延迟正确显示
- 新增语音回复延迟监控
- 提供延迟历史统计
