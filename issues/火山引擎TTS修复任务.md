# 火山引擎 TTS 双向流式 API 修复任务

## 问题现状

- 系统能接收到音频数据（日志显示收到多个音频片段）
- 但音频无法播放，影响用户体验
- 需要验证火山引擎双向流式 API 是否正确实现

## 解决方案：综合修复方案

### 阶段 1：独立 API 测试客户端（验证接口）✅ 已完成

**目标：** 创建独立的火山引擎双向流式 TTS 测试客户端

**完成情况：**

- ✅ 创建 `volcano-tts-test.html` - 完整的火山引擎 WebSocket 客户端
- ✅ 实现二进制协议处理（参考 Python demo）
- ✅ 支持复刻 2.0/混音 mix 功能
- ✅ 实时音频播放验证
- ✅ 修复浏览器环境 Buffer 兼容性问题
- ✅ 使用 ArrayBuffer 和 DataView 替代 Node.js Buffer

### 阶段 2：现有系统诊断修复 ✅ 已完成

**目标：** 修复现有音频播放功能

**完成情况：**

- ✅ 分析 `websocket-test.html` 音频播放逻辑
- ✅ 修复音频数据处理和播放功能
- ✅ 解决事件监听器重复添加问题
- ✅ 改进 playAudioBlob 和 playAudioBlobImmediately 方法
- ✅ 修复 handleStreamingAudioChunk 音频播放策略
- ✅ 更新 playNextInQueue 支持通用音频项处理

### 阶段 3：系统集成优化 🔄 进行中

**目标：** 集成验证过的解决方案

**进展情况：**

- ✅ 更新 `DoubaoTtsService.js` 使用正确协议
  - ✅ 修复 startSession 会话启动格式
  - ✅ 修复 sendTextToTts 文本发送格式
  - ✅ 修复 finishSession 会话结束格式
  - ✅ 修复 createHeader 和 createOptional 方法
  - ✅ 重写 parseResponse 使用火山引擎协议格式
  - ✅ 修复 sendEvent 方法参数和结构
- 🔄 测试音频传输和播放
- ⏳ 全流程测试验证

## 技术修复要点

### 协议修复

1. **消息格式**：改为标准火山引擎格式（header + optional + payloadSize + payload）
2. **会话启动**：使用正确的 session_id 和 tts 配置格式
3. **文本发送**：简化为 session_id + text 格式
4. **响应解析**：按标准 4+4+4+payload 格式解析

### 音频播放修复

1. **事件监听器管理**：使用 once 选项避免重复监听
2. **音频队列机制**：流式音频进入播放队列，顺序播放
3. **错误处理**：完善音频播放错误处理和恢复机制
4. **URL 管理**：正确管理和清理 blob URL

## 预期结果

- 火山引擎 TTS 能正常播放音频
- 现有语音对话系统完全可用
- 支持复刻 2.0 和混音功能

## 执行时间

- 已用时约 2 小时
- 预计再需 30 分钟完成最终测试验证
