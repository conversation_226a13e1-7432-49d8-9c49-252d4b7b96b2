# 豆包 TTS 服务实现

## 任务概述

实现豆包 TTS 服务，支持流式音频生成、格式转换、错误处理和 WebSocket 集成。

## 实现计划

### 阶段 0：环境修复 ✅

- [x] 安装项目依赖 (`npm install`)
- [x] 添加音频处理依赖 (`node-wav`, `fluent-ffmpeg`)

### 阶段 1：豆包 TTS 服务实现 ✅

- [x] 创建 `src/services/DoubaoTtsService.js`
- [x] 实现豆包 API 调用与签名生成
- [x] 实现流式音频生成 (`generateAudioStream`)
- [x] 音频格式转换 (PCM/MP3 适配微信小程序)
- [x] 错误处理和重试机制
- [x] 音频流优化和分片传输

### 阶段 2：系统集成与流式处理 ✅

- [x] 更新 `src/app.js` WebSocket 消息处理
- [x] 集成 TTS 服务到对话流程
- [x] 实现"文本 →TTS→ 音频流"管道
- [x] 句子级处理与 TTS 并行处理
- [x] 音频缓存队列机制

### 阶段 3：性能优化验证 🔄

- [ ] 实现预连接机制（讯飞+豆包）
- [ ] 优化 WebSocket 传输效率
- [ ] 性能测试与调优
- [ ] 验证首段音频<800ms 目标
- [ ] 内存和连接数优化

## 核心功能特性

### DoubaoTtsService 核心功能

- **API 调用**: 豆包 TTS API 集成，支持签名验证
- **流式处理**: 异步生成器支持音频流式输出
- **格式转换**: 支持 MP3/PCM 格式，适配微信小程序
- **分片传输**: 音频数据分片，优化传输效率
- **错误处理**: 3 次重试机制，指数退避策略
- **文本预处理**: 智能分句，支持长文本处理

### WebSocket 消息处理

- **chat**: 聊天消息，集成 TTS 自动语音回复
- **tts_request**: 直接 TTS 请求
- **audio_config**: 动态音频配置
- **实时反馈**: 首段音频时间监控

### 音频输出格式

```javascript
// 音频分片消息
{
  type: "audio_chunk",
  data: "base64编码音频数据",
  format: "mp3",
  chunkIndex: 0,
  totalChunks: 5,
  sequenceNumber: 1,
  isLast: false
}
```

## 技术实现亮点

1. **流式音频生成**: 使用异步生成器实现真正的流式处理
2. **智能分句**: 按标点和长度智能切分文本，支持并行 TTS
3. **格式适配**: PCM 格式支持微信小程序，MP3 支持 Web 应用
4. **性能监控**: 首段音频时间追踪，目标<800ms
5. **错误恢复**: 重试机制确保服务稳定性

## 当前状态

- ✅ 核心 TTS 服务已实现
- ✅ WebSocket 集成完成
- ✅ 流式处理管道搭建完成
- 🔄 等待性能优化和测试验证

## 下一步计划

1. 启动服务测试基础 TTS 功能
2. 实现预连接机制优化
3. 性能基准测试
4. 集成 AI 对话模型
