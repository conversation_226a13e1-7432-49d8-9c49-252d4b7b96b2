# 双向流式TTS实现文档

## 概述

本项目实现了基于火山引擎双向流式TTS API的真正实时语音合成系统。该系统能够在接收到AI模型流式输出的每个文本chunk时立即开始TTS合成，实现真正的实时语音输出。

## 核心特性

### 🚀 真正的流式TTS
- **立即处理**: 每收到AI chunk立即进行TTS合成，不等待完整响应
- **双向流式协议**: 实现火山引擎WebSocket二进制协议
- **队列管理**: 智能TTS队列管理，确保音频顺序播放
- **实时音频**: 首段音频延迟目标 <800ms

### 🔄 WebSocket双向通信
- **二进制协议**: 完整实现火山引擎双向流式TTS协议
- **连接管理**: 自动连接管理和重连机制
- **会话控制**: 完整的会话生命周期管理
- **错误处理**: 完善的错误处理和恢复机制

### 📊 性能优化
- **并发处理**: 支持多连接并发TTS处理
- **内存管理**: 智能音频缓存和清理
- **流量控制**: 防止音频数据积压
- **性能监控**: 详细的性能指标收集

## 技术架构

### 1. 双向流式TTS服务 (DoubaoTtsService)

#### 核心方法
```javascript
// 创建双向流式连接
async createBidirectionalConnection(connectionId)

// 双向流式TTS处理
async *generateBidirectionalAudioStream(connectionId, text, options)

// 立即TTS处理（用于AI chunk）
async *generateImmediateTts(connectionId, text, chunkIndex, options)
```

#### 协议实现
- **二进制协议头部**: 4字节固定头部
- **可选字段**: 事件、会话ID、连接ID
- **消息类型**: 客户端请求、服务端响应、音频数据、错误信息
- **事件处理**: 连接、会话、任务、音频响应事件

### 2. 应用层集成 (app.js)

#### 流式处理流程
1. **AI流式输出**: 接收AI模型的streaming chunks
2. **智能分割**: 根据内容长度和语义进行chunk分割
3. **立即TTS**: 每个chunk立即触发双向TTS处理
4. **音频流式**: 实时发送音频数据到客户端
5. **播放控制**: 支持音频播放中断和队列管理

#### 新增消息类型
- `immediate_bidirectional_tts_complete`: 双向TTS完成
- `immediate_bidirectional_tts_error`: 双向TTS错误
- `audio_stream`: 增强的音频流消息（支持双向标识）

### 3. 客户端支持

#### 测试界面 (test-immediate-tts.html)
- **实时监控**: 显示TTS处理状态和性能指标
- **音频播放**: 智能音频队列和播放控制
- **性能统计**: 延迟、吞吐量、错误率统计
- **调试信息**: 详细的日志和状态显示

#### 测试脚本 (test-bidirectional-tts.js)
- **自动化测试**: 命令行自动化测试工具
- **性能评估**: 自动性能评估和报告
- **错误检测**: 自动错误检测和统计

## 使用方法

### 1. 启动服务器
```bash
cd voice-chat-server
npm install
npm start
```

### 2. 网页测试
打开 `test-immediate-tts.html` 在浏览器中进行交互测试。

### 3. 命令行测试
```bash
node test-bidirectional-tts.js
```

### 4. 配置说明
在 `config/config.js` 中配置：
```javascript
DOUBAO: {
  APPID: "your-app-id",
  ACCESS_TOKEN: "your-access-token",
  SECRET_KEY: "your-secret-key",
  VOICE_TYPE: "zh_female_wanwanxiaohe_moon_bigtts"
}
```

## 性能指标

### 目标性能
- **首段音频延迟**: <800ms
- **音频连续性**: 无明显间断
- **内存使用**: 稳定，无泄漏
- **并发支持**: 支持多用户同时使用

### 监控指标
- `immediate_bidirectional_tts`: 双向TTS处理延迟
- `audioChunks`: 音频片段吞吐量
- `connectionStats`: 连接状态统计
- `errorRate`: 错误率监控

## 故障排除

### 常见问题

1. **连接失败**
   - 检查火山引擎API配置
   - 验证网络连接
   - 查看服务器日志

2. **音频延迟高**
   - 检查网络延迟
   - 优化文本chunk大小
   - 监控服务器性能

3. **音频播放中断**
   - 检查WebSocket连接稳定性
   - 验证音频格式支持
   - 查看浏览器控制台错误

### 调试方法
- 启用详细日志: 查看控制台输出
- 性能监控: 使用内置性能监控工具
- 网络分析: 检查WebSocket消息流
- 音频分析: 验证音频数据完整性

## 技术细节

### 双向流式协议
基于火山引擎官方文档实现的WebSocket二进制协议：
- 协议版本: v1
- 头部大小: 4字节
- 消息类型: 客户端请求/服务端响应/音频数据/错误信息
- 序列化: JSON/Raw二进制
- 压缩: 支持gzip压缩

### 事件流程
1. StartConnection → ConnectionStarted
2. StartSession → SessionStarted  
3. TaskRequest → TTSResponse (音频数据)
4. FinishSession → SessionFinished

### 音频处理
- 格式支持: MP3, PCM, OGG_OPUS
- 采样率: 8000-48000Hz
- 分片大小: 8KB
- 缓冲策略: 智能缓冲和预加载

## 扩展功能

### 未来改进
- [ ] 音频质量自适应调整
- [ ] 多语言支持优化
- [ ] 语音情感控制
- [ ] 实时语音中断和恢复
- [ ] 音频后处理效果

### 集成建议
- 可集成到现有聊天系统
- 支持微信小程序音频格式
- 可扩展支持其他TTS服务商
- 支持语音识别双向对话

## 许可证

本项目遵循项目根目录的许可证条款。
