# 延迟显示修复记录

## 问题描述

网页性能指标中，"平均延迟"和"首片延迟"都显示为 0ms，用户需要看到"首批文字开始合成语音的延迟"。

## 根本原因

1. **首片延迟计算错误**：只记录了时间戳，未计算与请求时间的差值
2. **延迟历史数据缺失**：没有正确记录延迟数据到`latencyHistory`数组
3. **语音请求基准不准确**：没有使用`voiceRequestTimestamp`作为基准时间

## 修复方案

### 1. 修复首片延迟计算

**文件**：`websocket-test.html`

- 在`handleAudioChunk`和`handleSentenceAudioChunk`中正确计算延迟
- 使用`voiceRequestTimestamp`作为起始时间
- 计算从请求发送到收到首个音频分片的真实延迟

### 2. 完善延迟历史管理

- 在心跳、音频首片等关键点记录延迟数据
- 实现历史记录自动清理（保留最近 50 条）
- 添加调试日志便于问题排查

### 3. 增强延迟显示

- 改进`updateLatencyDisplay`方法，添加无数据时的默认显示
- 确保延迟数据能正确显示在 UI 中

## 修复内容

### handleAudioChunk 修复前：

```javascript
if (this.audioChunks === 1) {
  this.firstChunkTime = Date.now();
  document.getElementById("firstChunkTime").textContent = "首片接收";
}
```

### handleAudioChunk 修复后：

```javascript
if (this.audioChunks === 1 && this.voiceRequestTimestamp) {
  this.firstChunkTime = Date.now() - this.voiceRequestTimestamp;
  document.getElementById(
    "firstChunkTime"
  ).textContent = `${this.firstChunkTime}ms`;

  // 记录到延迟历史中
  this.latencyHistory.push(this.firstChunkTime);
  this.updateLatencyDisplay();
}
```

### handleSentenceAudioChunk 增强：

- 计算语音请求到首片音频的真实延迟
- 同时更新首片延迟和平均延迟显示
- 记录到多个历史数据数组

## 预期效果

✅ **首片延迟**：显示从发送语音请求到收到首个音频分片的真实毫秒数
✅ **平均延迟**：显示所有延迟测量的平均值
✅ **准确追踪**：以用户语音请求为起点，准确计算"首批文字开始合成语音"的延迟

## 测试建议

1. 发送心跳包验证基础延迟显示
2. 发送带 TTS 的聊天消息验证首片音频延迟
3. 多次测试验证平均延迟计算正确性
