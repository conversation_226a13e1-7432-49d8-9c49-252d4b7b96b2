# 数字列表分句问题修复

## 问题描述

在语音合成时，数字列表（如"1. 商业营销"、"2. 服务行业"）被错误地分割，导致播放时会听到单独的"1, 2, 3"数字。

## 根本原因

`preprocessText` 方法中的分句正则表达式将所有点号（.）都作为句子分隔符，导致"1."被单独切分。

## 解决方案

1. 修改分句正则表达式，使用负向后视排除数字后的点号
2. 添加短片段合并逻辑，将过短的片段（<5 个字符）与后续片段合并

## 修改内容

文件：`src/services/DoubaoTtsService.js`

修改前：

```javascript
.split(/[。！？；：\.\!\?\;:]+/)
```

修改后：

```javascript
.split(/(?<!\d)[。！？；：\!\?\;:]|(?<!\d)\.(?!\s*[\u4e00-\u9fa5\w])/g)
```

同时添加了短片段合并逻辑，确保不会出现过短的独立音频片段。

## 测试结果

✅ 数字列表项保持完整（如"1. 商业营销"）
✅ 没有过短的片段（<5 个字符）
✅ 语音播放连贯，不再出现单独的数字读音
