# 数字列表播报和延迟显示修复

## 问题描述

1. **数字列表仍被分割**：尽管修复了 TTS 服务的分句逻辑，但 AI 服务仍会将"1."、"2."等作为独立句子发送
2. **延迟显示为 0**：用户需要看到"首批文字开始合成语音的延迟"，但显示仍为 0ms

## 根本原因

### 数字列表问题

- `AIModelService.js`的`extractCompleteSentences`方法使用了简单的正则表达式
- 正则`/([。！？.!?\n])/g`会将所有点号作为句子结束符
- 导致"1. 商业营销"在点号处被分割

### 延迟显示问题

- 前端只追踪了音频分片到达时间，而非 TTS 开始时间
- 用户真正关心的是从发送请求到 TTS 开始合成的延迟

## 修复方案

### 1. 修复 AI 服务句子分割

**文件**：`src/services/AIModelService.js`

修改前：

```javascript
const sentenceEnders = /([。！？.!?\n])/g;
```

修改后：

```javascript
// 排除数字后的点号（如 1. 2. 等）
const sentenceEnders =
  /([。！？]|(?<!\d)\.(?!\s*\w)|[!?]\s*(?=[A-Z\u4e00-\u9fa5])|[\n])/g;
```

同时添加了短句过滤逻辑，避免单独的数字被作为句子。

### 2. 增强前端延迟追踪

**文件**：`websocket-test.html`

在`sentence_tts_start`消息处理中添加：

- 计算从用户请求到 TTS 开始的真实延迟
- 更新首片延迟显示为 TTS 开始延迟
- 记录到延迟历史用于计算平均值

## 技术细节

### 改进的句子分割正则

- `(?<!\d)\.`：点号前不是数字（负向后视）
- `(?!\s*\w)`：点号后不是空格+字母数字（负向前视）
- 保留数字列表格式如"1. 文本"、"2. 文本"

### 延迟计算逻辑

1. 用户发送请求时记录`voiceRequestTimestamp`
2. 收到`ai_start`时计算文本响应延迟
3. 收到第一个`sentence_tts_start`时计算 TTS 开始延迟
4. 这个延迟就是"首批文字开始合成语音的延迟"

## 预期效果

✅ **数字列表完整播报**：不再听到单独的"1, 2, 3"
✅ **准确的延迟显示**：显示从请求到 TTS 开始的真实延迟
✅ **流畅的语音体验**：句子保持完整，播放连贯

## 测试要点

1. 发送包含数字列表的测试文本
2. 验证数字列表项是否作为完整句子播报
3. 检查首片延迟是否显示合理的毫秒数
4. 多次测试验证平均延迟计算准确性
