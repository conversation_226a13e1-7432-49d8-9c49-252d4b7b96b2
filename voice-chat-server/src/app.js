const Koa = require("koa");
const Router = require("koa-router");
const cors = require("koa-cors");
const bodyParser = require("koa-bodyparser");
const http = require("http");
const WebSocket = require("ws");
const config = require("../config/config");
const DoubaoTtsService = require("./services/DoubaoTtsService");
const AIModelService = require("./services/AIModelService");
const XfyunIatService = require("./services/XfyunIatService");
const ConnectionManager = require("./utils/ConnectionManager");
const StreamingProtocol = require("./utils/StreamingProtocol");
const PerformanceMonitor = require("./utils/PerformanceMonitor");

class VoiceChatServer {
  constructor() {
    this.app = new Koa();
    this.server = null;
    this.wss = null;

    // 初始化TTS服务
    this.ttsService = new DoubaoTtsService(config.DOUBAO);

    // 初始化AI模型服务
    this.aiService = new AIModelService(config.AI);

    // 初始化语音识别服务
    this.iatService = new XfyunIatService(config.XFYUN);

    // 连接管理器 - 增强的WebSocket连接管理
    this.connectionManager = new ConnectionManager();

    // 流式协议处理器
    this.streamingProtocol = new StreamingProtocol();

    // 性能监控器
    this.performanceMonitor = new PerformanceMonitor();

    // 音频缓存队列
    this.audioQueue = new Map(); // 按连接ID存储音频队列

    // 对话历史管理
    this.conversationHistory = new Map(); // 按连接ID存储对话历史

    // TTS队列管理 (connectionId -> { queue: [], processing: boolean })
    this.ttsQueues = new Map();

    // 连接状态管理
    this.connectionStates = new Map(); // 连接状态持久化

    // 流控制管理
    this.flowControl = new Map(); // 流量控制状态

    this.setupMiddleware();
    this.setupRoutes();
  }

  setupMiddleware() {
    // 跨域支持
    this.app.use(
      cors({
        origin: "*",
        credentials: true,
      })
    );

    // 请求体解析
    this.app.use(
      bodyParser({
        jsonLimit: "10mb",
        formLimit: "10mb",
      })
    );

    // 错误处理
    this.app.use(async (ctx, next) => {
      try {
        await next();
      } catch (err) {
        console.error("应用错误:", err);
        ctx.status = err.status || 500;
        ctx.body = {
          error: err.message || "服务器内部错误",
        };
      }
    });

    // 请求日志
    this.app.use(async (ctx, next) => {
      const start = Date.now();
      await next();
      const ms = Date.now() - start;
      console.log(`${ctx.method} ${ctx.url} - ${ms}ms`);
    });
  }

  setupRoutes() {
    const router = new Router();

    // 健康检查
    router.get("/health", (ctx) => {
      ctx.body = {
        status: "ok",
        timestamp: new Date().toISOString(),
        version: "1.0.0",
      };
    });

    // API路由
    router.get("/api/test", (ctx) => {
      ctx.body = { message: "语音对话服务运行正常" };
    });

    this.app.use(router.routes());
    this.app.use(router.allowedMethods());
  }

  setupWebSocket() {
    this.wss = new WebSocket.Server({
      server: this.server,
      perMessageDeflate: {
        zlibDeflateOptions: {
          level: 1,
          chunkSize: 1024,
        },
        threshold: 1024,
        concurrencyLimit: 10,
        clientMaxWindowBits: 15,
        serverMaxWindowBits: 15,
        serverMaxNoContextTakeover: false,
        clientMaxNoContextTakeover: false,
      },
    });

    this.wss.on("connection", (ws, req) => {
      console.log(`新的WebSocket连接: ${req.socket.remoteAddress}`);

      // 使用连接管理器注册连接
      const connectionInfo = this.connectionManager.registerConnection(ws, req);
      if (!connectionInfo) {
        return; // 连接被拒绝
      }

      // 初始化流控制
      this.streamingProtocol.initFlowControl(connectionInfo.id);

      // 初始化性能监控
      this.performanceMonitor.startTimer(`connection_${connectionInfo.id}`, {
        ip: connectionInfo.ip,
        userAgent: connectionInfo.userAgent,
      });

      // 消息处理
      ws.on("message", async (message) => {
        try {
          // 记录消息处理开始
          const messageId = `msg_${Date.now()}_${Math.random()
            .toString(36)
            .substr(2, 9)}`;
          this.performanceMonitor.startTimer(messageId, {
            type: "message_processing",
          });
          this.performanceMonitor.recordThroughput(
            "messages",
            1,
            message.length
          );

          const data = JSON.parse(message);
          await this.handleWebSocketMessage(ws, data);

          // 记录消息处理完成
          this.performanceMonitor.endTimer(messageId, "total");
        } catch (error) {
          console.error("WebSocket消息处理错误:", error);
          this.performanceMonitor.recordError("connection", error);

          ws.send(
            JSON.stringify({
              type: "error",
              message: "消息处理失败",
              timestamp: Date.now(),
            })
          );
        }
      });

      // 连接关闭处理
      ws.on("close", (code, reason) => {
        const connectionId = connectionInfo.id;
        console.log(`WebSocket连接已关闭: ${connectionId}`);

        // 清理对话历史
        this.conversationHistory.delete(connectionId);

        // 清理音频队列
        this.audioQueue.delete(connectionId);

        // 清理TTS队列
        this.ttsQueues.delete(connectionId);

        // 清理双向TTS连接
        this.ttsService.cleanupConnection(connectionId).catch((error) => {
          console.error(`清理双向TTS连接失败 ${connectionId}:`, error);
        });

        // 清理流控制状态
        this.streamingProtocol.cleanupFlowControl(connectionId);

        // 记录连接生命周期
        this.performanceMonitor.endTimer(
          `connection_${connectionId}`,
          "connection"
        );
      });

      // 错误处理
      ws.on("error", (error) => {
        console.error(`连接错误 ${connectionInfo.id}:`, error);
        this.performanceMonitor.recordError("connection", error);
      });

      // 发送增强的欢迎消息
      const welcomeMessage = {
        type: "connected",
        message: "已连接到语音对话服务",
        connectionId: connectionInfo.id,
        serverCapabilities: {
          streaming: true,
          compression: true,
          flowControl: true,
          adaptiveChunking: true,
        },
        timestamp: Date.now(),
      };

      this.connectionManager.sendToConnection(
        connectionInfo.id,
        welcomeMessage
      );
    });

    // 更新连接指标
    setInterval(() => {
      const stats = this.connectionManager.getStats();
      this.performanceMonitor.updateConnectionMetrics(
        stats.activeConnections,
        stats.totalConnections,
        stats.activeConnections, // 简化的峰值计算
        0 // 平均生命周期，需要更复杂的计算
      );
    }, 5000);

    console.log("增强的WebSocket服务已启动");
  }

  async handleWebSocketMessage(ws, data) {
    console.log("收到消息:", data.type);
    const connectionId = ws.connectionId;

    switch (data.type) {
      case "ping":
        // 增强的ping/pong处理，包含延迟测量
        const pongMessage = {
          type: "pong",
          timestamp: data.timestamp || Date.now(),
          serverTime: Date.now(),
        };

        // 如果有时间戳，计算延迟
        if (data.timestamp) {
          const latency = Date.now() - data.timestamp;
          this.performanceMonitor.recordLatency("ping", latency);
          pongMessage.latency = latency;
        }

        this.connectionManager.sendToConnection(connectionId, pongMessage);
        break;

      case "chat":
        this.performanceMonitor.startTimer(
          `chat_${connectionId}_${Date.now()}`,
          { type: "chat" }
        );
        await this.handleChatMessage(ws, data);
        break;

      case "tts_request":
        this.performanceMonitor.startTimer(
          `tts_${connectionId}_${Date.now()}`,
          { type: "tts" }
        );
        await this.handleTtsRequest(ws, data);
        break;

      case "audio_config":
        await this.handleAudioConfig(ws, data);
        break;

      case "flow_control_ack":
        // 处理流控制确认
        this.streamingProtocol.handleFlowControlAck(
          connectionId,
          data.bytesAcked || 0
        );
        break;

      case "stop_audio":
        // 停止音频播放
        await this.handleStopAudio(ws, data);
        break;

      case "performance_request":
        // 发送性能报告
        const performanceReport =
          this.performanceMonitor.getPerformanceReport();
        const connectionStats = this.connectionManager.getStats();
        const flowStats =
          this.streamingProtocol.getFlowControlStats(connectionId);

        this.connectionManager.sendToConnection(connectionId, {
          type: "performance_report",
          performance: performanceReport,
          connection: connectionStats,
          flowControl: flowStats,
          timestamp: Date.now(),
        });
        break;

      default:
        this.connectionManager.sendToConnection(connectionId, {
          type: "error",
          message: "不支持的消息类型",
          supportedTypes: [
            "ping",
            "chat",
            "tts_request",
            "audio_config",
            "flow_control_ack",
            "stop_audio",
            "performance_request",
          ],
          timestamp: Date.now(),
        });
    }
  }

  /**
   * 处理聊天消息 - 集成AI流式对话和TTS流式处理
   */
  async handleChatMessage(ws, data) {
    try {
      const { content, enableTts = true, audioFormat = "mp3" } = data;
      const connectionId = ws.connectionId;

      // 开始AI处理计时
      const aiTimerId = `ai_${connectionId}_${Date.now()}`;
      this.performanceMonitor.startTimer(aiTimerId, {
        type: "ai_processing",
        contentLength: content.length,
        enableTts,
      });

      // 清空该连接的TTS队列（新对话开始）
      const ttsQueue = this.getTtsQueue(connectionId);
      ttsQueue.queue = [];
      ttsQueue.processing = false;

      // 发送文本确认
      this.connectionManager.sendToConnection(connectionId, {
        type: "text_received",
        content: content,
        timestamp: Date.now(),
      });

      // 获取对话历史
      const history = this.conversationHistory.get(connectionId) || [];

      // 格式化消息
      const messages = this.aiService.formatMessages(content, history);

      // 发送AI开始回复信号
      this.connectionManager.sendToConnection(connectionId, {
        type: "ai_start",
        message: "AI正在思考中...",
        timestamp: Date.now(),
      });

      let fullResponse = "";
      let chunkIndex = 0;
      let processedChunks = new Set(); // 添加已处理chunk的集合，用于去重

      // 使用AI流式对话
      await this.aiService.chatStream(
        messages,

        // onChunk: 处理AI流式输出
        async (chunk) => {
          if (chunk.type === "text_stream") {
            // 使用流式协议发送文本
            const textMessage = this.streamingProtocol.createTextStreamMessage(
              chunk.content,
              { type: "ai_text_stream" }
            );

            this.connectionManager.sendToConnection(connectionId, textMessage);
            this.performanceMonitor.recordThroughput(
              "textChunks",
              1,
              chunk.content.length
            );

            fullResponse += chunk.content;
          } else if (chunk.type === "immediate_tts_chunk") {
            // 🚀 新的立即TTS处理逻辑
            const chunkContent = chunk.content.trim();

            // 过滤空文本和纯标点符号
            const meaningfulText = chunkContent.replace(
              /[^\u4e00-\u9fa5a-zA-Z0-9]/g,
              ""
            );
            if (meaningfulText.length < 2) {
              console.log(`[过滤] 跳过无意义文本: "${chunkContent}"`);
              return;
            }

            // 去重检查：避免重复处理相同的chunk
            const chunkHash = `${chunk.chunkId}_${chunkContent}`;
            if (processedChunks.has(chunkHash)) {
              console.log(
                `[去重] 跳过重复chunk: "${chunkContent.substring(0, 30)}..."`
              );
              return;
            }

            if (chunkContent.length > 0) {
              chunkIndex++;
              processedChunks.add(chunkHash);

              console.log(
                `🚀 [立即TTS] 处理chunk [${chunkIndex}]: "${chunkContent}"`
              );

              // 发送立即TTS开始信号
              this.connectionManager.sendToConnection(connectionId, {
                type: "immediate_tts_start",
                content: chunkContent,
                chunkIndex: chunkIndex,
                chunkId: chunk.chunkId,
                isFinal: chunk.isFinal,
                timestamp: Date.now(),
              });

              // 如果启用TTS，立即开始TTS处理（使用双向流式TTS）
              if (enableTts) {
                // 立即处理TTS，使用新的双向流式方法
                this.processImmediateBidirectionalTts(
                  ws,
                  chunkContent,
                  chunkIndex,
                  audioFormat,
                  chunk.isFinal
                );
              }
            }
          }
        },

        // onComplete: AI回复完成
        async () => {
          // 记录AI处理完成
          const aiDuration = this.performanceMonitor.endTimer(aiTimerId, "ai");

          // 发送AI回复完成信号
          this.connectionManager.sendToConnection(connectionId, {
            type: "ai_complete",
            fullResponse: fullResponse.trim(),
            totalChunks: chunkIndex,
            processingTime: aiDuration,
            timestamp: Date.now(),
          });

          // 更新对话历史
          history.push(
            { role: "user", content: content },
            { role: "assistant", content: fullResponse.trim() }
          );

          // 保持历史记录在合理长度内（最近10轮对话）
          if (history.length > 20) {
            history.splice(0, history.length - 20);
          }

          this.conversationHistory.set(connectionId, history);
        },

        // onError: 错误处理
        (error) => {
          console.error("AI对话流错误:", error);
          this.performanceMonitor.recordError("ai", error);
          this.performanceMonitor.endTimer(aiTimerId, "ai");

          this.connectionManager.sendToConnection(connectionId, {
            type: "ai_error",
            error: error.message || "AI服务暂时不可用",
            timestamp: Date.now(),
          });
        }
      );
    } catch (error) {
      console.error("处理聊天消息失败:", error);
      this.performanceMonitor.recordError("chat", error);

      this.connectionManager.sendToConnection(connectionId, {
        type: "error",
        message: "聊天消息处理失败",
        details: error.message,
        timestamp: Date.now(),
      });
    }
  }

  /**
   * 处理停止音频播放请求
   */
  async handleStopAudio(ws, data) {
    try {
      const connectionId = ws.connectionId;
      const { stopAll = true, sentenceIndex = null } = data;

      console.log(`🛑 [停止音频] 连接 ${connectionId} 请求停止音频播放`);

      if (stopAll) {
        // 停止所有音频播放
        const ttsQueue = this.getTtsQueue(connectionId);

        // 清空TTS队列
        const queueLength = ttsQueue.queue.length;
        ttsQueue.queue = [];
        ttsQueue.processing = false;

        console.log(
          `🛑 [停止音频] 已清空TTS队列，取消了 ${queueLength} 个待处理任务`
        );

        // 发送停止确认
        this.connectionManager.sendToConnection(connectionId, {
          type: "audio_stopped",
          message: "所有音频播放已停止",
          cancelledTasks: queueLength,
          timestamp: Date.now(),
        });
      } else if (sentenceIndex !== null) {
        // 停止特定句子的音频播放
        const ttsQueue = this.getTtsQueue(connectionId);

        // 从队列中移除指定句子
        const originalLength = ttsQueue.queue.length;
        ttsQueue.queue = ttsQueue.queue.filter(
          (task) => task.sentenceIndex !== sentenceIndex
        );
        const removedCount = originalLength - ttsQueue.queue.length;

        console.log(
          `🛑 [停止音频] 已从队列中移除句子 ${sentenceIndex}，移除了 ${removedCount} 个任务`
        );

        // 发送停止确认
        this.connectionManager.sendToConnection(connectionId, {
          type: "audio_stopped",
          message: `句子 ${sentenceIndex} 的音频播放已停止`,
          sentenceIndex: sentenceIndex,
          cancelledTasks: removedCount,
          timestamp: Date.now(),
        });
      }

      // 记录性能指标
      this.performanceMonitor.recordThroughput("audioInterruptions", 1);
    } catch (error) {
      console.error("处理停止音频请求失败:", error);
      this.performanceMonitor.recordError("audio_stop", error);

      this.connectionManager.sendToConnection(ws.connectionId, {
        type: "error",
        message: "停止音频播放失败",
        details: error.message,
        timestamp: Date.now(),
      });
    }
  }

  /**
   * 获取或创建连接的TTS队列
   */
  getTtsQueue(connectionId) {
    if (!this.ttsQueues.has(connectionId)) {
      this.ttsQueues.set(connectionId, {
        queue: [],
        processing: false,
      });
    }
    return this.ttsQueues.get(connectionId);
  }

  /**
   * 添加TTS任务到队列
   */
  async enqueueTtsTask(ws, sentence, sentenceIndex, audioFormat) {
    const connectionId = this.getConnectionId(ws);
    const ttsQueue = this.getTtsQueue(connectionId);

    // 添加任务到队列
    ttsQueue.queue.push({
      ws,
      sentence,
      sentenceIndex,
      audioFormat,
      timestamp: Date.now(),
    });

    console.log(
      `[TTS队列] 句子${sentenceIndex}已加入队列，当前队列长度: ${ttsQueue.queue.length}`
    );

    // 如果队列未在处理，开始处理
    if (!ttsQueue.processing) {
      await this.processTtsQueue(connectionId);
    }
  }

  /**
   * 处理TTS队列，确保按顺序执行
   */
  async processTtsQueue(connectionId) {
    const ttsQueue = this.getTtsQueue(connectionId);

    if (ttsQueue.processing) {
      return; // 已在处理中
    }

    ttsQueue.processing = true;

    while (ttsQueue.queue.length > 0) {
      const task = ttsQueue.queue.shift();
      const { ws, sentence, sentenceIndex, audioFormat } = task;

      try {
        console.log(
          `[TTS队列] 开始处理句子${sentenceIndex}: "${sentence.substring(
            0,
            50
          )}${sentence.length > 50 ? "..." : ""}"`
        );
        await this.processSentenceTts(ws, sentence, sentenceIndex, audioFormat);
        console.log(`[TTS队列] 句子${sentenceIndex}处理完成`);
      } catch (error) {
        console.error(`[TTS队列] 句子${sentenceIndex}处理失败:`, error);

        // 发送错误消息到客户端
        ws.send(
          JSON.stringify({
            type: "sentence_tts_error",
            sentenceIndex: sentenceIndex,
            error: error.message,
          })
        );
      }
    }

    ttsQueue.processing = false;
    console.log(`[TTS队列] 连接${connectionId}的队列处理完成`);
  }

  /**
   * 处理单个句子的TTS转换（同步队列处理）
   */
  async processSentenceTts(ws, sentence, sentenceIndex, audioFormat) {
    const connectionId = ws.connectionId;
    const ttsTimerId = `tts_${connectionId}_${sentenceIndex}_${Date.now()}`;

    try {
      // 开始TTS计时
      this.performanceMonitor.startTimer(ttsTimerId, {
        type: "tts_processing",
        sentenceIndex,
        sentenceLength: sentence.length,
      });

      // 发送TTS开始信号
      this.connectionManager.sendToConnection(connectionId, {
        type: "sentence_tts_start",
        sentence: sentence,
        sentenceIndex: sentenceIndex,
        format: audioFormat,
        timestamp: Date.now(),
      });

      const startTime = Date.now();

      // 生成TTS音频流
      const audioStream = this.ttsService.generateAudioStream(sentence, {
        format: audioFormat,
        enableChunking: true,
      });

      let chunkCount = 0;
      let firstChunkTime = null;

      for await (const chunk of audioStream) {
        if (chunk.type === "audio_chunk") {
          chunkCount++;

          // 记录首段音频时间
          if (!firstChunkTime) {
            firstChunkTime = Date.now() - startTime;
            this.performanceMonitor.recordLatency("tts", firstChunkTime);
          }

          // 使用流式协议创建音频消息
          const audioMessage = this.streamingProtocol.createAudioStreamMessage(
            chunk.data,
            {
              format: chunk.format,
              sequenceNumber: chunk.sequenceNumber || chunkCount,
              totalChunks: chunk.totalChunks,
              sentenceIndex: sentenceIndex,
              isLast: chunk.isLast,
            }
          );

          this.connectionManager.sendToConnection(connectionId, audioMessage);
          this.performanceMonitor.recordThroughput(
            "audioChunks",
            1,
            chunk.data.length
          );
        } else if (chunk.type === "complete") {
          const totalTime = Date.now() - startTime;
          this.performanceMonitor.endTimer(ttsTimerId, "tts");

          // 发送句子TTS完成信号
          this.connectionManager.sendToConnection(connectionId, {
            type: "sentence_tts_complete",
            sentenceIndex: sentenceIndex,
            totalTime: totalTime,
            totalChunks: chunkCount,
            firstChunkTime: firstChunkTime,
            timestamp: Date.now(),
          });
        } else if (chunk.type === "error") {
          this.performanceMonitor.recordError("tts", new Error(chunk.error));
          this.performanceMonitor.endTimer(ttsTimerId, "tts");

          this.connectionManager.sendToConnection(connectionId, {
            type: "sentence_tts_error",
            sentenceIndex: sentenceIndex,
            error: chunk.error,
            timestamp: Date.now(),
          });
        }
      }
    } catch (error) {
      console.error(`句子TTS处理失败 [${sentenceIndex}]:`, error);
      this.performanceMonitor.recordError("tts", error);
      this.performanceMonitor.endTimer(ttsTimerId, "tts");

      this.connectionManager.sendToConnection(connectionId, {
        type: "sentence_tts_error",
        sentenceIndex: sentenceIndex,
        error: error.message,
        timestamp: Date.now(),
      });
    }
  }

  /**
   * 提取部分句子进行实时TTS
   */
  extractPartialSentence(text) {
    // 寻找自然的断点：逗号、分号、冒号等
    const breakPoints = [
      "，",
      "。",
      "！",
      "？",
      "；",
      "：",
      ",",
      ".",
      "!",
      "?",
      ";",
      ":",
      "、",
      "…",
      "——",
      "—",
    ];

    let bestBreakPoint = -1;
    let bestBreakChar = "";

    // 从后往前找最近的断点，降低最小长度要求
    for (let i = text.length - 1; i >= 5; i--) {
      const char = text[i];
      if (breakPoints.includes(char)) {
        bestBreakPoint = i;
        bestBreakChar = char;
        break;
      }
    }

    if (bestBreakPoint > 4) {
      // 包含断点字符，降低最小长度要求
      return text.substring(0, bestBreakPoint + 1);
    }

    // 如果没有找到断点，但文本足够长，取前面的部分
    if (text.length >= 10) {
      // 降低长度要求，尝试在词边界断开
      const words = text.split(/\s+/);
      if (words.length >= 2) {
        const partialWords = words.slice(0, Math.floor(words.length / 2));
        const partial = partialWords.join(" ");
        if (partial.length >= 5) {
          return partial;
        }
      }
    }

    // 更激进的策略：如果文本长度超过8个字符，直接取前6个字符
    if (text.length >= 8) {
      return text.substring(0, 6);
    }

    return null;
  }

  /**
   * 处理立即双向流式TTS（真正的实时处理）
   */
  async processImmediateBidirectionalTts(
    ws,
    content,
    chunkIndex,
    audioFormat,
    isFinal = false
  ) {
    const connectionId = ws.connectionId;
    const ttsTimerId = `immediate_bidirectional_tts_${connectionId}_${chunkIndex}_${Date.now()}`;

    try {
      console.log(
        `🚀 [立即双向TTS] 开始处理chunk [${chunkIndex}]: "${content}" (isFinal: ${isFinal})`
      );

      // 开始TTS计时
      this.performanceMonitor.startTimer(ttsTimerId, {
        type: "immediate_bidirectional_tts_processing",
        chunkIndex,
        contentLength: content.length,
        isFinal,
      });

      const startTime = Date.now();

      // 使用双向流式TTS
      const audioStream = this.ttsService.generateImmediateTts(
        connectionId,
        content,
        chunkIndex,
        {
          format: audioFormat,
        }
      );

      let audioChunkCount = 0;
      let firstChunkTime = null;

      for await (const audioChunk of audioStream) {
        if (audioChunk.type === "audio_chunk") {
          audioChunkCount++;

          // 记录首段音频时间
          if (!firstChunkTime) {
            firstChunkTime = Date.now() - startTime;
            this.performanceMonitor.recordLatency(
              "immediate_bidirectional_tts",
              firstChunkTime
            );
            console.log(
              `🚀 [立即双向TTS] 首段音频生成时间: ${firstChunkTime}ms`
            );
          }

          // 使用流式协议创建音频消息
          const audioMessage = this.streamingProtocol.createAudioStreamMessage(
            audioChunk.data,
            {
              format: audioChunk.format,
              sequenceNumber: audioChunk.sequenceNumber || audioChunkCount,
              totalChunks: audioChunk.totalChunks,
              chunkIndex: chunkIndex,
              isLast: audioChunk.isLast,
              isFinal: isFinal,
              isImmediate: true,
              isBidirectional: true,
            }
          );

          this.connectionManager.sendToConnection(connectionId, audioMessage);
          this.performanceMonitor.recordThroughput(
            "audioChunks",
            1,
            audioChunk.data.length
          );
        } else if (audioChunk.type === "complete") {
          const totalTime = Date.now() - startTime;
          this.performanceMonitor.endTimer(
            ttsTimerId,
            "immediate_bidirectional_tts"
          );

          console.log(
            `🚀 [立即双向TTS] chunk处理完成 [${chunkIndex}]，耗时: ${totalTime}ms`
          );

          // 发送立即双向TTS完成信号
          this.connectionManager.sendToConnection(connectionId, {
            type: "immediate_bidirectional_tts_complete",
            chunkIndex: chunkIndex,
            totalTime: totalTime,
            totalChunks: audioChunkCount,
            firstChunkTime: firstChunkTime,
            isFinal: isFinal,
            isImmediate: true,
            isBidirectional: true,
            timestamp: Date.now(),
          });
        } else if (audioChunk.type === "error") {
          this.performanceMonitor.recordError(
            "immediate_bidirectional_tts",
            new Error(audioChunk.error)
          );
          this.performanceMonitor.endTimer(
            ttsTimerId,
            "immediate_bidirectional_tts"
          );

          this.connectionManager.sendToConnection(connectionId, {
            type: "immediate_bidirectional_tts_error",
            chunkIndex: chunkIndex,
            error: audioChunk.error,
            isFinal: isFinal,
            isImmediate: true,
            isBidirectional: true,
            timestamp: Date.now(),
          });
        }
      }
    } catch (error) {
      console.error(`🚀 [立即双向TTS] chunk处理失败 [${chunkIndex}]:`, error);
      this.performanceMonitor.recordError("immediate_bidirectional_tts", error);
      this.performanceMonitor.endTimer(
        ttsTimerId,
        "immediate_bidirectional_tts"
      );

      this.connectionManager.sendToConnection(connectionId, {
        type: "immediate_bidirectional_tts_error",
        chunkIndex: chunkIndex,
        error: error.message,
        isFinal: isFinal,
        isImmediate: true,
        isBidirectional: true,
        timestamp: Date.now(),
      });
    }
  }

  /**
   * 处理立即TTS（真正的实时处理）- 保持向后兼容
   */
  async processImmediateTts(
    ws,
    content,
    chunkIndex,
    audioFormat,
    isFinal = false
  ) {
    const connectionId = ws.connectionId;
    const ttsTimerId = `immediate_tts_${connectionId}_${chunkIndex}_${Date.now()}`;

    try {
      console.log(
        `🚀 [立即TTS] 开始处理chunk [${chunkIndex}]: "${content}" (isFinal: ${isFinal})`
      );

      // 开始TTS计时
      this.performanceMonitor.startTimer(ttsTimerId, {
        type: "immediate_tts_processing",
        chunkIndex,
        contentLength: content.length,
        isFinal,
      });

      const startTime = Date.now();

      // 生成TTS音频流
      const audioStream = this.ttsService.generateAudioStream(content, {
        format: audioFormat,
        enableChunking: true,
      });

      let audioChunkCount = 0;
      let firstChunkTime = null;

      for await (const audioChunk of audioStream) {
        if (audioChunk.type === "audio_chunk") {
          audioChunkCount++;

          // 记录首段音频时间
          if (!firstChunkTime) {
            firstChunkTime = Date.now() - startTime;
            this.performanceMonitor.recordLatency(
              "immediate_tts",
              firstChunkTime
            );
            console.log(`🚀 [立即TTS] 首段音频生成时间: ${firstChunkTime}ms`);
          }

          // 使用流式协议创建音频消息
          const audioMessage = this.streamingProtocol.createAudioStreamMessage(
            audioChunk.data,
            {
              format: audioChunk.format,
              sequenceNumber: audioChunk.sequenceNumber || audioChunkCount,
              totalChunks: audioChunk.totalChunks,
              chunkIndex: chunkIndex,
              isLast: audioChunk.isLast,
              isFinal: isFinal,
              isImmediate: true,
            }
          );

          this.connectionManager.sendToConnection(connectionId, audioMessage);
          this.performanceMonitor.recordThroughput(
            "audioChunks",
            1,
            audioChunk.data.length
          );
        } else if (audioChunk.type === "complete") {
          const totalTime = Date.now() - startTime;
          this.performanceMonitor.endTimer(ttsTimerId, "immediate_tts");

          console.log(
            `🚀 [立即TTS] chunk处理完成 [${chunkIndex}]，耗时: ${totalTime}ms`
          );

          // 发送立即TTS完成信号
          this.connectionManager.sendToConnection(connectionId, {
            type: "immediate_tts_complete",
            chunkIndex: chunkIndex,
            totalTime: totalTime,
            totalChunks: audioChunkCount,
            firstChunkTime: firstChunkTime,
            isFinal: isFinal,
            isImmediate: true,
            timestamp: Date.now(),
          });
        } else if (audioChunk.type === "error") {
          this.performanceMonitor.recordError(
            "immediate_tts",
            new Error(audioChunk.error)
          );
          this.performanceMonitor.endTimer(ttsTimerId, "immediate_tts");

          this.connectionManager.sendToConnection(connectionId, {
            type: "immediate_tts_error",
            chunkIndex: chunkIndex,
            error: audioChunk.error,
            isFinal: isFinal,
            isImmediate: true,
            timestamp: Date.now(),
          });
        }
      }
    } catch (error) {
      console.error(`🚀 [立即TTS] chunk处理失败 [${chunkIndex}]:`, error);
      this.performanceMonitor.recordError("immediate_tts", error);
      this.performanceMonitor.endTimer(ttsTimerId, "immediate_tts");

      this.connectionManager.sendToConnection(connectionId, {
        type: "immediate_tts_error",
        chunkIndex: chunkIndex,
        error: error.message,
        isFinal: isFinal,
        isImmediate: true,
        timestamp: Date.now(),
      });
    }
  }

  /**
   * 处理部分句子的TTS（实时处理）
   */
  async processPartialTts(ws, sentence, sentenceIndex, audioFormat) {
    const connectionId = ws.connectionId;
    const ttsTimerId = `partial_tts_${connectionId}_${sentenceIndex}_${Date.now()}`;

    try {
      console.log(
        `🚀 [实时TTS] 开始处理部分句子 [${sentenceIndex}]: "${sentence}"`
      );

      // 开始TTS计时
      this.performanceMonitor.startTimer(ttsTimerId, {
        type: "partial_tts_processing",
        sentenceIndex,
        sentenceLength: sentence.length,
        isPartial: true,
      });

      const startTime = Date.now();

      // 生成TTS音频流
      const audioStream = this.ttsService.generateAudioStream(sentence, {
        format: audioFormat,
        enableChunking: true,
      });

      let chunkCount = 0;
      let firstChunkTime = null;

      for await (const chunk of audioStream) {
        if (chunk.type === "audio_chunk") {
          chunkCount++;

          // 记录首段音频时间
          if (!firstChunkTime) {
            firstChunkTime = Date.now() - startTime;
            this.performanceMonitor.recordLatency(
              "partial_tts",
              firstChunkTime
            );
            console.log(`🚀 [实时TTS] 首段音频生成时间: ${firstChunkTime}ms`);
          }

          // 使用流式协议创建音频消息
          const audioMessage = this.streamingProtocol.createAudioStreamMessage(
            chunk.data,
            {
              format: chunk.format,
              sequenceNumber: chunk.sequenceNumber || chunkCount,
              totalChunks: chunk.totalChunks,
              sentenceIndex: sentenceIndex,
              isLast: chunk.isLast,
              isPartial: true,
            }
          );

          this.connectionManager.sendToConnection(connectionId, audioMessage);
          this.performanceMonitor.recordThroughput(
            "audioChunks",
            1,
            chunk.data.length
          );
        } else if (chunk.type === "complete") {
          const totalTime = Date.now() - startTime;
          this.performanceMonitor.endTimer(ttsTimerId, "partial_tts");

          console.log(
            `🚀 [实时TTS] 部分句子处理完成 [${sentenceIndex}]，耗时: ${totalTime}ms`
          );

          // 发送部分句子TTS完成信号
          this.connectionManager.sendToConnection(connectionId, {
            type: "partial_tts_complete",
            sentenceIndex: sentenceIndex,
            totalTime: totalTime,
            totalChunks: chunkCount,
            firstChunkTime: firstChunkTime,
            isPartial: true,
            timestamp: Date.now(),
          });
        } else if (chunk.type === "error") {
          this.performanceMonitor.recordError(
            "partial_tts",
            new Error(chunk.error)
          );
          this.performanceMonitor.endTimer(ttsTimerId, "partial_tts");

          this.connectionManager.sendToConnection(connectionId, {
            type: "partial_tts_error",
            sentenceIndex: sentenceIndex,
            error: chunk.error,
            isPartial: true,
            timestamp: Date.now(),
          });
        }
      }
    } catch (error) {
      console.error(`🚀 [实时TTS] 部分句子处理失败 [${sentenceIndex}]:`, error);
      this.performanceMonitor.recordError("partial_tts", error);
      this.performanceMonitor.endTimer(ttsTimerId, "partial_tts");

      this.connectionManager.sendToConnection(connectionId, {
        type: "partial_tts_error",
        sentenceIndex: sentenceIndex,
        error: error.message,
        isPartial: true,
        timestamp: Date.now(),
      });
    }
  }

  /**
   * 获取连接ID（兼容性方法）
   */
  getConnectionId(ws) {
    return ws.connectionId || null;
  }

  /**
   * 处理TTS请求
   */
  async handleTtsRequest(ws, data) {
    try {
      const { text, format = "mp3", streaming = true } = data;

      if (!text || text.trim().length === 0) {
        ws.send(
          JSON.stringify({
            type: "error",
            message: "文本内容为空",
          })
        );
        return;
      }

      await this.generateAndStreamTts(ws, text, { format, streaming });
    } catch (error) {
      console.error("处理TTS请求失败:", error);
      ws.send(
        JSON.stringify({
          type: "error",
          message: "TTS请求处理失败",
        })
      );
    }
  }

  /**
   * 生成并流式传输TTS音频
   */
  async generateAndStreamTts(ws, text, options = {}) {
    const { format = "mp3", streaming = true } = options;

    try {
      // 发送TTS开始信号
      ws.send(
        JSON.stringify({
          type: "tts_start",
          text: text,
          format: format,
        })
      );

      const startTime = Date.now();

      if (streaming) {
        // 流式处理
        const audioStream = this.ttsService.generateAudioStream(text, {
          format: format,
          enableChunking: true,
        });

        let firstChunkSent = false;
        let chunkCount = 0;

        for await (const chunk of audioStream) {
          if (chunk.type === "audio_chunk") {
            chunkCount++;

            // 记录首段音频时间
            if (!firstChunkSent) {
              const firstChunkTime = Date.now() - startTime;
              console.log(`首段音频生成时间: ${firstChunkTime}ms`);
              firstChunkSent = true;
            }

            // 发送音频分片，使用正确的sequenceNumber
            const audioMessage = {
              type: "audio_chunk",
              data: chunk.data.toString("base64"),
              format: chunk.format,
              chunkIndex: chunk.chunkIndex,
              totalChunks: chunk.totalChunks,
              sequenceNumber: chunk.sequenceNumber || chunkCount, // 使用chunk的sequenceNumber
              isLast: chunk.isLast,
              timestamp: Date.now(),
            };

            ws.send(JSON.stringify(audioMessage));
          } else if (chunk.type === "audio_complete") {
            // 完整音频块
            ws.send(
              JSON.stringify({
                type: "audio_chunk",
                data: chunk.data.toString("base64"),
                format: chunk.format,
                chunkIndex: chunk.chunkIndex,
                totalChunks: chunk.totalChunks,
                sequenceNumber: chunk.sequenceNumber || 1, // 使用chunk的sequenceNumber
                isLast: chunk.isLast,
                timestamp: Date.now(),
              })
            );
          } else if (chunk.type === "error") {
            ws.send(
              JSON.stringify({
                type: "tts_error",
                error: chunk.error,
                chunkIndex: chunk.chunkIndex,
              })
            );
          } else if (chunk.type === "complete") {
            const totalTime = Date.now() - startTime;
            console.log(`TTS生成完成，耗时: ${totalTime}ms`);
            ws.send(
              JSON.stringify({
                type: "tts_complete",
                message: chunk.message,
                totalTime: totalTime,
                totalChunks: chunkCount,
              })
            );
          }
        }
      } else {
        // 非流式处理
        const result = await this.ttsService.generateAudio(text, { format });

        if (result.success) {
          const totalTime = Date.now() - startTime;
          console.log(`TTS生成完成，耗时: ${totalTime}ms`);

          ws.send(
            JSON.stringify({
              type: "audio_complete",
              data: result.audioData.toString("base64"),
              format: result.format,
              totalTime: totalTime,
            })
          );
        } else {
          ws.send(
            JSON.stringify({
              type: "tts_error",
              error: result.error,
            })
          );
        }
      }
    } catch (error) {
      console.error("TTS流式处理失败:", error);
      ws.send(
        JSON.stringify({
          type: "tts_error",
          error: error.message,
        })
      );
    }
  }

  /**
   * 处理音频配置
   */
  async handleAudioConfig(ws, data) {
    try {
      const { format, sampleRate, channels, bitDepth } = data;

      // 更新TTS服务音频配置
      if (format) this.ttsService.audioConfig.format = format;
      if (sampleRate) this.ttsService.audioConfig.sample_rate = sampleRate;
      if (channels) this.ttsService.audioConfig.channels = channels;
      if (bitDepth) this.ttsService.audioConfig.bit_depth = bitDepth;

      ws.send(
        JSON.stringify({
          type: "audio_config_updated",
          config: this.ttsService.audioConfig,
        })
      );
    } catch (error) {
      console.error("更新音频配置失败:", error);
      ws.send(
        JSON.stringify({
          type: "error",
          message: "音频配置更新失败",
        })
      );
    }
  }

  start() {
    const port = config.PORT;

    this.server = http.createServer(this.app.callback());
    this.setupWebSocket();

    // 设置性能监控事件监听
    this.setupPerformanceMonitoring();

    this.server.listen(port, () => {
      console.log(`🚀 增强的语音对话服务已启动`);
      console.log(`📡 HTTP服务: http://localhost:${port}`);
      console.log(`🔌 WebSocket服务: ws://localhost:${port}`);
      console.log(`🌍 环境: ${config.NODE_ENV}`);
      console.log(`📊 性能监控: 已启用`);
      console.log(`🔄 流控制: 已启用`);
      console.log(`📦 数据压缩: 已启用`);
    });

    // 优雅关闭处理
    process.on("SIGTERM", () => this.gracefulShutdown());
    process.on("SIGINT", () => this.gracefulShutdown());
  }

  /**
   * 设置性能监控事件监听
   */
  setupPerformanceMonitoring() {
    this.performanceMonitor.on("alerts", (alerts) => {
      console.warn("🚨 性能告警:", alerts);
      // 可以在这里添加告警通知逻辑
    });

    this.performanceMonitor.on("metricsCollected", (metrics) => {
      // 定期输出关键指标
      if (metrics.connections.active > 0) {
        console.log(
          `📊 活跃连接: ${metrics.connections.active}, 内存使用: ${(
            metrics.system.memory.percentage * 100
          ).toFixed(1)}%`
        );
      }
    });

    this.connectionManager.on("connectionRegistered", (connectionInfo) => {
      console.log(`✅ 新连接: ${connectionInfo.id} (${connectionInfo.ip})`);
    });

    this.connectionManager.on("connectionClosed", (connectionInfo, details) => {
      console.log(
        `❌ 连接关闭: ${connectionInfo.id} (持续时间: ${details.duration}ms)`
      );
    });
  }

  /**
   * 优雅关闭
   */
  async gracefulShutdown() {
    console.log("🔄 开始优雅关闭服务器...");

    try {
      // 停止接受新连接
      this.server.close();

      // 停止性能监控
      this.performanceMonitor.stopMonitoring();

      // 关闭所有WebSocket连接
      this.connectionManager.stop();

      // 导出性能数据
      const performanceData = this.performanceMonitor.exportData();
      console.log("📊 性能数据已导出");

      console.log("✅ 服务器已优雅关闭");
      process.exit(0);
    } catch (error) {
      console.error("❌ 关闭过程中出现错误:", error);
      process.exit(1);
    }
  }
}

module.exports = VoiceChatServer;
