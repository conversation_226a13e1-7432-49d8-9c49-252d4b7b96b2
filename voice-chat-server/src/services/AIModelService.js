const axios = require("axios");
const config = require("../../config/config");

class AIModelService {
  constructor() {
    this.apiClient = axios.create({
      baseURL: config.AI.API_URL,
      headers: {
        Authorization: `Bearer ${config.AI.API_KEY}`,
        "Content-Type": "application/json",
      },
      timeout: 30000, // 30秒超时
    });
  }

  // 流式对话 - 真正的实时TTS流式处理
  async chatStream(messages, onChunk, onComplete, onError) {
    try {
      const requestData = {
        model: config.AI.MODEL,
        messages: messages,
        stream: true,
        temperature: 0.7,
        max_tokens: 2000,
      };

      console.log("发送AI请求:", JSON.stringify(requestData, null, 2));

      const response = await this.apiClient.post(
        "", // 使用空字符串，因为baseURL已经包含完整路径
        requestData,
        {
          responseType: "stream",
        }
      );

      let buffer = "";
      let chunkBuffer = ""; // 用于累积chunk进行TTS
      let chunkId = 0;
      let isCompleted = false; // 添加完成标志，避免重复处理
      const MIN_CHUNK_LENGTH = 6; // 最小chunk长度，适当提高以减少并发请求
      const MAX_CHUNK_LENGTH = 15; // 最大chunk长度，避免chunk过长

      response.data.on("data", (chunk) => {
        buffer += chunk.toString();

        // 处理服务器发送事件 (SSE) 格式
        const lines = buffer.split("\n");
        buffer = lines.pop() || ""; // 保留不完整的行

        for (const line of lines) {
          const trimmedLine = line.trim();

          if (trimmedLine.startsWith("data: ")) {
            const data = trimmedLine.slice(6);

            if (data === "[DONE]") {
              if (isCompleted) return; // 避免重复处理

              // 处理剩余的chunk缓冲
              if (chunkBuffer.trim()) {
                onChunk({
                  type: "immediate_tts_chunk",
                  content: chunkBuffer.trim(),
                  chunkId: chunkId++,
                  isFinal: true,
                });
                chunkBuffer = ""; // 清空缓冲，避免重复处理
              }

              isCompleted = true;
              onComplete();
              return;
            }

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices?.[0]?.delta?.content;

              if (content) {
                // 发送文本流（保持原有功能）
                onChunk({
                  type: "text_stream",
                  content: content,
                });

                // 累积chunk缓冲用于立即TTS
                chunkBuffer += content;

                // 🚀 立即TTS策略：智能chunk分割
                if (chunkBuffer.length >= MIN_CHUNK_LENGTH) {
                  // 检查是否包含有意义的文本（过滤纯标点符号）
                  const meaningfulText = chunkBuffer.replace(
                    /[^\u4e00-\u9fa5a-zA-Z0-9]/g,
                    ""
                  );

                  if (meaningfulText.length >= 2) {
                    // 至少包含2个有意义字符
                    // 立即发送TTS chunk，不等待句子完成
                    onChunk({
                      type: "immediate_tts_chunk",
                      content: chunkBuffer.trim(),
                      chunkId: chunkId++,
                      isFinal: false,
                    });

                    // 清空缓冲，准备下一个chunk
                    chunkBuffer = "";
                  } else if (chunkBuffer.length >= MAX_CHUNK_LENGTH) {
                    // 如果chunk太长但没有足够有意义的文本，强制分割
                    onChunk({
                      type: "immediate_tts_chunk",
                      content: chunkBuffer.trim(),
                      chunkId: chunkId++,
                      isFinal: false,
                    });
                    chunkBuffer = "";
                  }
                }
              }
            } catch (parseError) {
              console.warn("解析流数据错误:", parseError.message);
            }
          }
        }
      });

      response.data.on("error", (error) => {
        console.error("AI模型流错误:", error);
        onError(error);
      });

      response.data.on("end", () => {
        if (isCompleted) {
          return; // 如果已经通过[DONE]完成，避免重复处理
        }

        // 如果 [DONE] 没有被正确处理，在这里处理剩余内容
        if (chunkBuffer.trim()) {
          onChunk({
            type: "immediate_tts_chunk",
            content: chunkBuffer.trim(),
            chunkId: chunkId++,
            isFinal: true,
          });
        }

        isCompleted = true;
        onComplete();
      });
    } catch (error) {
      console.error("AI模型请求错误:", error);
      if (error.response) {
        console.error("错误响应状态:", error.response.status);
        console.error("错误响应数据:", error.response.data);
      }
      onError(error);
    }
  }

  // 非流式对话
  async chat(messages) {
    try {
      const requestData = {
        model: config.AI.MODEL,
        messages: messages,
        stream: false,
        temperature: 0.7,
        max_tokens: 2000,
      };

      const response = await this.apiClient.post("", requestData);

      return {
        content: response.data.choices[0].message.content,
        usage: response.data.usage,
      };
    } catch (error) {
      console.error("AI模型请求错误:", error);
      throw error;
    }
  }

  // 智能句子分割
  extractCompleteSentences(text) {
    const completed = [];
    // 改进的正则：排除数字后的点号（如 1. 2. 等）
    // 匹配中文句号、感叹号、问号、英文句号（但不是数字后的）、英文感叹号、问号、换行
    const sentenceEnders =
      /([。！？]|(?<!\d)\.(?!\s*\w)|[!?]\s*(?=[A-Z\u4e00-\u9fa5])|[\n])/g;
    let lastIndex = 0;
    let match;

    while ((match = sentenceEnders.exec(text)) !== null) {
      const sentence = text
        .slice(lastIndex, match.index + match[0].length)
        .trim();
      if (sentence.length > 0) {
        // 避免将过短的片段（如单独的数字）作为句子
        if (sentence.length > 2 || /[\u4e00-\u9fa5]/.test(sentence)) {
          completed.push(sentence);
          lastIndex = match.index + match[0].length;
        }
      }
    }

    return {
      completed,
      remaining: text.slice(lastIndex),
    };
  }

  // 格式化消息历史
  formatMessages(userMessage, conversationHistory = []) {
    const messages = [];

    // 添加系统提示
    messages.push({
      role: "system",
      content: "",
    });

    // 添加历史对话
    conversationHistory.forEach((msg) => {
      messages.push(msg);
    });

    // 添加当前用户消息
    messages.push({
      role: "user",
      content: userMessage,
    });

    return messages;
  }

  // 健康检查
  async healthCheck() {
    try {
      // 发送一个简单的非流式请求测试连接
      const testMessages = [
        {
          role: "user",
          content: "Hello",
        },
      ];

      const response = await this.apiClient.post("", {
        model: config.AI.MODEL,
        messages: testMessages,
        stream: false,
        max_tokens: 10,
      });

      return response.status === 200;
    } catch (error) {
      console.error("AI模型健康检查失败:", error);
      return false;
    }
  }
}

module.exports = AIModelService;
