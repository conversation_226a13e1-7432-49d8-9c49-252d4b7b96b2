const axios = require("axios");
const crypto = require("crypto-js");
const { Transform } = require("stream");
const wav = require("node-wav");

class DoubaoTtsService {
  constructor(config) {
    this.appId = config.APPID;
    this.accessToken = config.ACCESS_TOKEN;
    this.secretKey = config.SECRET_KEY;
    this.voiceType = config.VOICE_TYPE;
    this.ttsUrl = "https://openspeech.bytedance.com/api/v1/tts";

    // 音频配置
    this.audioConfig = {
      format: "mp3", // 默认MP3格式
      sample_rate: 24000,
      bit_depth: 16,
      channels: 1,
    };

    // 重试配置
    this.retryConfig = {
      maxRetries: 3,
      retryDelay: 1000,
      backoffFactor: 2,
    };

    // 分片配置
    this.chunkConfig = {
      maxTextLength: 1000, // 进一步增加最大文本长度
      chunkSize: 1024 * 8, // 音频分片大小 8KB
    };
  }

  /**
   * 生成豆包TTS签名
   */
  generateSignature(timestamp, text) {
    const signString = `${this.appId}${timestamp}${text}${this.secretKey}`;
    return crypto.MD5(signString).toString();
  }

  /**
   * 文本预处理和分句 - 特别处理数字列表，确保顺序
   */
  preprocessText(text) {
    // 首先移除markdown格式符号
    let cleanText = text
      // 移除粗体标记 **text** 和 __text__
      .replace(/\*\*(.*?)\*\*/g, "$1")
      .replace(/__(.*?)__/g, "$1")
      // 移除斜体标记 *text* 和 _text_
      .replace(/\*(.*?)\*/g, "$1")
      .replace(/_(.*?)_/g, "$1")
      // 移除删除线 ~~text~~
      .replace(/~~(.*?)~~/g, "$1")
      // 移除代码块标记 `code` 和 ```code```
      .replace(/```[\s\S]*?```/g, "")
      .replace(/`([^`]*)`/g, "$1")
      // 移除链接 [text](url)
      .replace(/\[([^\]]*)\]\([^)]*\)/g, "$1")
      // 移除图片 ![alt](url)
      .replace(/!\[[^\]]*\]\([^)]*\)/g, "")
      // 移除标题标记 # ## ### 等
      .replace(/^#{1,6}\s+/gm, "")
      // 移除列表标记前的 - * +
      .replace(/^[\s]*[-\*\+]\s+/gm, "")
      // 移除剩余的单独星号和其他特殊符号
      .replace(/[^\u4e00-\u9fa5\w\s\.,!?;:，。！？；：\-()（）]/g, "")
      .trim();

    // 如果文本长度在限制内，直接返回整个文本
    if (cleanText.length <= this.chunkConfig.maxTextLength) {
      console.log("文本长度适中，不进行分割:", cleanText);
      return [cleanText];
    }

    // 识别数字列表模式，避免在列表项之间分割
    const chunks = [];
    let currentChunk = "";
    let inNumberedList = false;
    let i = 0;

    while (i < cleanText.length) {
      const char = cleanText[i];
      const nextFewChars = cleanText.substring(i, i + 20);

      // 检测数字列表的开始 (如 "1. " 或 "2. " 等) - 移除对星号的依赖
      if (/^\d+\.\s+/.test(nextFewChars)) {
        // 如果当前块不为空且我们遇到了新的列表项
        if (currentChunk.trim().length > 0 && inNumberedList) {
          // 检查是否在合理的分割点
          if (currentChunk.length >= this.chunkConfig.maxTextLength * 0.6) {
            chunks.push(currentChunk.trim());
            currentChunk = "";
          }
        }
        inNumberedList = true;
      }

      currentChunk += char;

      // 只在非列表状态下考虑在句号处分割
      if (!inNumberedList && /[。！？]/.test(char)) {
        if (currentChunk.length >= this.chunkConfig.maxTextLength * 0.5) {
          chunks.push(currentChunk.trim());
          currentChunk = "";
        }
      }
      // 检测列表项的结束 (下一个列表项开始或段落结束)
      else if (inNumberedList && /[。！？]/.test(char)) {
        // 向前看是否有新的列表项
        const remaining = cleanText.substring(i + 1).trim();
        if (!remaining.match(/^\s*\d+\.\s+/)) {
          // 没有下一个列表项，列表结束
          inNumberedList = false;
        }
      }

      // 强制分割过长的文本
      if (currentChunk.length >= this.chunkConfig.maxTextLength) {
        // 在数字列表中，尽量不分割
        if (inNumberedList) {
          // 只有在特别长的情况下才分割
          if (currentChunk.length >= this.chunkConfig.maxTextLength * 1.5) {
            // 寻找段落分割点
            let splitPoint = this.findSafeSplitPoint(currentChunk);
            if (splitPoint > 0) {
              chunks.push(currentChunk.substring(0, splitPoint).trim());
              currentChunk = currentChunk.substring(splitPoint).trim();
            }
          }
        } else {
          // 非列表状态，正常分割
          let splitPoint = this.findSafeSplitPoint(currentChunk);
          if (splitPoint > 0) {
            chunks.push(currentChunk.substring(0, splitPoint).trim());
            currentChunk = currentChunk.substring(splitPoint).trim();
          } else {
            chunks.push(currentChunk.trim());
            currentChunk = "";
          }
        }
      }

      i++;
    }

    // 添加剩余文本
    if (currentChunk.trim().length > 0) {
      chunks.push(currentChunk.trim());
    }

    // 过滤空块并确保顺序
    const filteredChunks = chunks.filter((chunk) => chunk.length > 0);

    console.log("文本分割结果（保护数字列表）:");
    filteredChunks.forEach((chunk, index) => {
      console.log(
        `${index + 1}: ${chunk.substring(0, 150)}${
          chunk.length > 150 ? "..." : ""
        }`
      );
    });

    return filteredChunks;
  }

  /**
   * 寻找安全的分割点
   */
  findSafeSplitPoint(text) {
    const maxSearchLength = Math.min(100, text.length);

    // 优先在句号处分割
    for (let i = text.length - 1; i >= text.length - maxSearchLength; i--) {
      if (/[。！？]/.test(text[i])) {
        return i + 1;
      }
    }

    // 其次在逗号处分割
    for (let i = text.length - 1; i >= text.length - maxSearchLength; i--) {
      if (/[，,；;]/.test(text[i])) {
        return i + 1;
      }
    }

    // 最后在空格处分割
    for (let i = text.length - 1; i >= text.length - maxSearchLength; i--) {
      if (/\s/.test(text[i])) {
        return i + 1;
      }
    }

    return 0; // 找不到合适分割点
  }

  /**
   * 调用豆包TTS API
   */
  async callTtsApi(text, retryCount = 0) {
    try {
      const timestamp = Date.now().toString();

      // 使用正确的豆包TTS API格式
      const requestData = {
        app: {
          appid: this.appId,
          token: this.accessToken,
          cluster: "volcano_tts",
        },
        user: {
          uid: "default_user",
        },
        audio: {
          voice_type: this.voiceType,
          encoding: this.audioConfig.format,
          rate: this.audioConfig.sample_rate,
        },
        request: {
          reqid: `tts_${timestamp}`,
          text: text,
          text_type: "plain",
          operation: "query",
        },
      };

      console.log("发送TTS请求:", JSON.stringify(requestData, null, 2));

      const response = await axios.post(this.ttsUrl, requestData, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer;${this.accessToken}`,
        },
        timeout: 30000,
      });

      console.log("TTS响应状态:", response.status);
      console.log("TTS响应数据类型:", typeof response.data);

      if (response.status === 200 && response.data) {
        // 检查返回的数据格式
        if (response.data.code !== undefined) {
          if (response.data.code === 3000) {
            // 成功响应，data字段包含base64编码的音频
            const audioData = Buffer.from(response.data.data, "base64");
            return {
              success: true,
              audioData: audioData,
              format: this.audioConfig.format,
              sequence: response.data.sequence,
              duration: response.data.addition?.duration,
            };
          } else {
            throw new Error(
              `TTS API错误: ${response.data.message} (错误码: ${response.data.code})`
            );
          }
        } else {
          // 直接返回音频数据
          return {
            success: true,
            audioData: response.data,
            format: this.audioConfig.format,
          };
        }
      } else {
        throw new Error(`TTS API响应错误: ${response.status}`);
      }
    } catch (error) {
      console.error("TTS API调用失败:", error.message);

      // 输出详细错误信息用于调试
      if (error.response) {
        console.error("错误响应状态:", error.response.status);
        console.error("错误响应数据:", error.response.data);
        console.error("错误响应头:", error.response.headers);
      }

      // 重试逻辑
      if (retryCount < this.retryConfig.maxRetries) {
        const delay =
          this.retryConfig.retryDelay *
          Math.pow(this.retryConfig.backoffFactor, retryCount);
        console.log(
          `${delay}ms后重试 (${retryCount + 1}/${this.retryConfig.maxRetries})`
        );

        await this.sleep(delay);
        return this.callTtsApi(text, retryCount + 1);
      }

      return {
        success: false,
        error: error.message,
        details: error.response?.data || null,
      };
    }
  }

  /**
   * 音频格式转换
   */
  async convertAudioFormat(audioData, targetFormat = "mp3") {
    try {
      if (targetFormat === "pcm") {
        // 转换为PCM格式 (适配微信小程序)
        const decoded = wav.decode(audioData);
        return {
          success: true,
          audioData: decoded.channelData[0], // 单声道PCM数据
          format: "pcm",
          sampleRate: decoded.sampleRate,
        };
      } else if (targetFormat === "mp3") {
        // MP3格式直接返回
        return {
          success: true,
          audioData: audioData,
          format: "mp3",
        };
      } else {
        throw new Error(`不支持的音频格式: ${targetFormat}`);
      }
    } catch (error) {
      console.error("音频格式转换失败:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 创建音频流分片器
   */
  createAudioChunker(chunkSize = this.chunkConfig.chunkSize) {
    return new Transform({
      transform(chunk, encoding, callback) {
        let offset = 0;
        while (offset < chunk.length) {
          const end = Math.min(offset + chunkSize, chunk.length);
          const audioChunk = chunk.slice(offset, end);
          this.push(audioChunk);
          offset = end;
        }
        callback();
      },
    });
  }

  /**
   * 流式TTS处理 - 绝对按照文本原始顺序
   */
  async *generateAudioStream(text, options = {}) {
    const { format = "mp3", enableChunking = true } = options;

    try {
      // 文本预处理 - 保护数字列表
      const textChunks = this.preprocessText(text);
      console.log(`文本分为${textChunks.length}个片段，严格按原始顺序处理`);

      // 绝对按照数组索引顺序处理，不使用任何异步并发
      for (let i = 0; i < textChunks.length; i++) {
        const chunk = textChunks[i];
        if (chunk.trim().length === 0) continue;

        console.log(
          `[序号${i + 1}] 正在处理: "${chunk.substring(0, 100)}${
            chunk.length > 100 ? "..." : ""
          }"`
        );

        // 同步处理，确保顺序
        const ttsResult = await this.callTtsApi(chunk);

        if (ttsResult.success) {
          const convertResult = await this.convertAudioFormat(
            ttsResult.audioData,
            format
          );

          if (convertResult.success) {
            if (enableChunking) {
              // 直接分割音频数据，不使用Transform流
              const audioData = convertResult.audioData;
              const chunkSize = this.chunkConfig.chunkSize;
              let chunkSequence = 1;

              for (
                let offset = 0;
                offset < audioData.length;
                offset += chunkSize
              ) {
                const end = Math.min(offset + chunkSize, audioData.length);
                const audioChunk = audioData.slice(offset, end);

                yield {
                  type: "audio_chunk",
                  data: audioChunk,
                  format: convertResult.format,
                  chunkIndex: i,
                  totalChunks: textChunks.length,
                  isLast:
                    i === textChunks.length - 1 && end === audioData.length,
                  originalText: chunk,
                  sequenceNumber: chunkSequence++,
                  timestamp: Date.now(),
                };
              }
            } else {
              yield {
                type: "audio_complete",
                data: convertResult.audioData,
                format: convertResult.format,
                chunkIndex: i,
                totalChunks: textChunks.length,
                isLast: i === textChunks.length - 1,
                originalText: chunk,
                sequenceNumber: i + 1,
                timestamp: Date.now(),
              };
            }

            console.log(`[序号${i + 1}] 处理完成`);
          } else {
            yield {
              type: "error",
              error: `音频转换失败: ${convertResult.error}`,
              chunkIndex: i,
              sequenceNumber: i + 1,
            };
          }
        } else {
          yield {
            type: "error",
            error: `TTS生成失败: ${ttsResult.error}`,
            details: ttsResult.details,
            chunkIndex: i,
            sequenceNumber: i + 1,
          };
        }
      }

      yield {
        type: "complete",
        message: "TTS处理完成",
        timestamp: Date.now(),
      };
    } catch (error) {
      console.error("流式TTS处理错误:", error);
      yield {
        type: "error",
        error: error.message,
      };
    }
  }

  /**
   * 简单TTS生成（非流式）
   */
  async generateAudio(text, options = {}) {
    const { format = "mp3" } = options;

    try {
      const ttsResult = await this.callTtsApi(text);

      if (ttsResult.success) {
        const convertResult = await this.convertAudioFormat(
          ttsResult.audioData,
          format
        );
        return convertResult;
      } else {
        return ttsResult;
      }
    } catch (error) {
      console.error("TTS生成错误:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 工具方法：延迟
   */
  sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      service: "DoubaoTTS",
      voiceType: this.voiceType,
      audioConfig: this.audioConfig,
      retryConfig: this.retryConfig,
      chunkConfig: this.chunkConfig,
    };
  }
}

module.exports = DoubaoTtsService;
