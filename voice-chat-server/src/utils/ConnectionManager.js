const EventEmitter = require('events');

/**
 * 增强的WebSocket连接管理器
 * 提供连接池、重连机制、状态管理等功能
 */
class ConnectionManager extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      maxConnections: options.maxConnections || 1000,
      heartbeatInterval: options.heartbeatInterval || 30000,
      reconnectAttempts: options.reconnectAttempts || 5,
      reconnectDelay: options.reconnectDelay || 1000,
      connectionTimeout: options.connectionTimeout || 60000,
      ...options
    };

    // 连接池
    this.connections = new Map(); // connectionId -> connection info
    this.activeConnections = new Set(); // 活跃连接集合
    
    // 连接统计
    this.stats = {
      totalConnections: 0,
      activeConnections: 0,
      reconnections: 0,
      errors: 0,
      bytesTransferred: 0,
      messagesProcessed: 0
    };

    // 心跳管理
    this.heartbeatTimer = null;
    this.startHeartbeat();

    console.log('ConnectionManager initialized with options:', this.options);
  }

  /**
   * 注册新连接
   */
  registerConnection(ws, req) {
    const connectionId = this.generateConnectionId();
    const connectionInfo = {
      id: connectionId,
      ws: ws,
      ip: req.socket.remoteAddress,
      userAgent: req.headers['user-agent'],
      connectedAt: Date.now(),
      lastActivity: Date.now(),
      isAlive: true,
      reconnectCount: 0,
      messageCount: 0,
      bytesReceived: 0,
      bytesSent: 0,
      latency: 0,
      state: 'connected'
    };

    // 检查连接数限制
    if (this.connections.size >= this.options.maxConnections) {
      console.warn(`Connection limit reached (${this.options.maxConnections}), rejecting new connection`);
      ws.close(1013, 'Server overloaded');
      return null;
    }

    this.connections.set(connectionId, connectionInfo);
    this.activeConnections.add(connectionId);
    this.stats.totalConnections++;
    this.stats.activeConnections++;

    // 设置WebSocket属性
    ws.connectionId = connectionId;
    ws.isAlive = true;

    // 绑定事件处理器
    this.bindConnectionEvents(ws, connectionInfo);

    console.log(`New connection registered: ${connectionId} from ${connectionInfo.ip}`);
    this.emit('connectionRegistered', connectionInfo);

    return connectionInfo;
  }

  /**
   * 绑定连接事件处理器
   */
  bindConnectionEvents(ws, connectionInfo) {
    // Pong响应处理
    ws.on('pong', () => {
      connectionInfo.isAlive = true;
      connectionInfo.lastActivity = Date.now();
      this.updateConnectionLatency(connectionInfo);
    });

    // 消息处理
    ws.on('message', (data) => {
      connectionInfo.messageCount++;
      connectionInfo.bytesReceived += data.length;
      connectionInfo.lastActivity = Date.now();
      this.stats.messagesProcessed++;
      this.stats.bytesTransferred += data.length;
    });

    // 连接关闭处理
    ws.on('close', (code, reason) => {
      this.unregisterConnection(connectionInfo.id, code, reason);
    });

    // 错误处理
    ws.on('error', (error) => {
      console.error(`Connection error for ${connectionInfo.id}:`, error);
      this.stats.errors++;
      this.emit('connectionError', connectionInfo, error);
    });
  }

  /**
   * 注销连接
   */
  unregisterConnection(connectionId, code, reason) {
    const connectionInfo = this.connections.get(connectionId);
    if (!connectionInfo) return;

    this.connections.delete(connectionId);
    this.activeConnections.delete(connectionId);
    this.stats.activeConnections--;

    const duration = Date.now() - connectionInfo.connectedAt;
    console.log(`Connection ${connectionId} closed after ${duration}ms (code: ${code}, reason: ${reason})`);
    
    this.emit('connectionClosed', connectionInfo, { code, reason, duration });
  }

  /**
   * 获取连接信息
   */
  getConnection(connectionId) {
    return this.connections.get(connectionId);
  }

  /**
   * 获取所有活跃连接
   */
  getActiveConnections() {
    return Array.from(this.activeConnections).map(id => this.connections.get(id));
  }

  /**
   * 发送消息到指定连接
   */
  sendToConnection(connectionId, data) {
    const connectionInfo = this.connections.get(connectionId);
    if (!connectionInfo || connectionInfo.ws.readyState !== 1) {
      return false;
    }

    try {
      const message = typeof data === 'string' ? data : JSON.stringify(data);
      connectionInfo.ws.send(message);
      connectionInfo.bytesSent += message.length;
      this.stats.bytesTransferred += message.length;
      return true;
    } catch (error) {
      console.error(`Failed to send message to ${connectionId}:`, error);
      return false;
    }
  }

  /**
   * 广播消息到所有连接
   */
  broadcast(data, excludeConnectionId = null) {
    let successCount = 0;
    const message = typeof data === 'string' ? data : JSON.stringify(data);

    for (const connectionId of this.activeConnections) {
      if (connectionId !== excludeConnectionId) {
        if (this.sendToConnection(connectionId, message)) {
          successCount++;
        }
      }
    }

    return successCount;
  }

  /**
   * 启动心跳检测
   */
  startHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
    }

    this.heartbeatTimer = setInterval(() => {
      this.performHeartbeat();
    }, this.options.heartbeatInterval);

    console.log(`Heartbeat started with interval: ${this.options.heartbeatInterval}ms`);
  }

  /**
   * 执行心跳检测
   */
  performHeartbeat() {
    const now = Date.now();
    const deadConnections = [];

    for (const [connectionId, connectionInfo] of this.connections) {
      if (!connectionInfo.isAlive) {
        // 连接已死，标记为待清理
        deadConnections.push(connectionId);
      } else if (now - connectionInfo.lastActivity > this.options.connectionTimeout) {
        // 连接超时，标记为待清理
        deadConnections.push(connectionId);
      } else {
        // 发送ping
        connectionInfo.isAlive = false;
        try {
          connectionInfo.ws.ping();
        } catch (error) {
          console.error(`Failed to ping connection ${connectionId}:`, error);
          deadConnections.push(connectionId);
        }
      }
    }

    // 清理死连接
    for (const connectionId of deadConnections) {
      const connectionInfo = this.connections.get(connectionId);
      if (connectionInfo) {
        console.log(`Terminating dead connection: ${connectionId}`);
        try {
          connectionInfo.ws.terminate();
        } catch (error) {
          console.error(`Error terminating connection ${connectionId}:`, error);
        }
        this.unregisterConnection(connectionId, 1006, 'Heartbeat timeout');
      }
    }

    // 输出统计信息
    if (this.connections.size > 0) {
      console.log(`Heartbeat check completed. Active connections: ${this.connections.size}`);
    }
  }

  /**
   * 更新连接延迟
   */
  updateConnectionLatency(connectionInfo) {
    const now = Date.now();
    // 简单的延迟估算，实际应用中可以更精确
    connectionInfo.latency = now - connectionInfo.lastActivity;
  }

  /**
   * 生成连接ID
   */
  generateConnectionId() {
    return `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      ...this.stats,
      activeConnections: this.connections.size,
      averageLatency: this.calculateAverageLatency(),
      uptime: Date.now() - (this.startTime || Date.now())
    };
  }

  /**
   * 计算平均延迟
   */
  calculateAverageLatency() {
    if (this.connections.size === 0) return 0;
    
    const totalLatency = Array.from(this.connections.values())
      .reduce((sum, conn) => sum + conn.latency, 0);
    
    return Math.round(totalLatency / this.connections.size);
  }

  /**
   * 停止连接管理器
   */
  stop() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }

    // 关闭所有连接
    for (const [connectionId, connectionInfo] of this.connections) {
      try {
        connectionInfo.ws.close(1001, 'Server shutting down');
      } catch (error) {
        console.error(`Error closing connection ${connectionId}:`, error);
      }
    }

    this.connections.clear();
    this.activeConnections.clear();
    
    console.log('ConnectionManager stopped');
  }
}

module.exports = ConnectionManager;
