const EventEmitter = require('events');

/**
 * 性能监控器
 * 监控系统性能、延迟、吞吐量等指标
 */
class PerformanceMonitor extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      metricsInterval: options.metricsInterval || 10000, // 10秒
      historySize: options.historySize || 100, // 保留100个历史记录
      alertThresholds: {
        latency: options.latencyThreshold || 1000, // 1秒
        memoryUsage: options.memoryThreshold || 0.8, // 80%
        cpuUsage: options.cpuThreshold || 0.8, // 80%
        errorRate: options.errorRateThreshold || 0.1, // 10%
        ...options.alertThresholds
      },
      ...options
    };

    // 性能指标
    this.metrics = {
      // 延迟指标
      latency: {
        ai: [], // AI响应延迟
        tts: [], // TTS生成延迟
        audio: [], // 音频传输延迟
        total: [] // 总延迟
      },
      
      // 吞吐量指标
      throughput: {
        messages: 0,
        audioChunks: 0,
        bytesTransferred: 0,
        connectionsPerSecond: 0
      },
      
      // 错误指标
      errors: {
        total: 0,
        ai: 0,
        tts: 0,
        connection: 0,
        rate: 0
      },
      
      // 系统资源
      system: {
        memory: { used: 0, total: 0, percentage: 0 },
        cpu: { percentage: 0 },
        uptime: 0
      },
      
      // 连接指标
      connections: {
        active: 0,
        total: 0,
        peak: 0,
        averageLifetime: 0
      }
    };

    // 历史数据
    this.history = [];
    
    // 计时器
    this.timers = new Map(); // 用于测量操作耗时
    
    // 监控定时器
    this.monitorTimer = null;
    
    // 启动时间
    this.startTime = Date.now();
    
    this.startMonitoring();
    console.log('PerformanceMonitor initialized');
  }

  /**
   * 启动性能监控
   */
  startMonitoring() {
    if (this.monitorTimer) {
      clearInterval(this.monitorTimer);
    }

    this.monitorTimer = setInterval(() => {
      this.collectMetrics();
      this.checkAlerts();
      this.saveSnapshot();
    }, this.options.metricsInterval);

    console.log(`Performance monitoring started with interval: ${this.options.metricsInterval}ms`);
  }

  /**
   * 停止性能监控
   */
  stopMonitoring() {
    if (this.monitorTimer) {
      clearInterval(this.monitorTimer);
      this.monitorTimer = null;
    }
    console.log('Performance monitoring stopped');
  }

  /**
   * 开始计时
   */
  startTimer(operationId, metadata = {}) {
    this.timers.set(operationId, {
      startTime: Date.now(),
      metadata
    });
  }

  /**
   * 结束计时并记录延迟
   */
  endTimer(operationId, category = 'total') {
    const timer = this.timers.get(operationId);
    if (!timer) {
      console.warn(`Timer not found for operation: ${operationId}`);
      return 0;
    }

    const duration = Date.now() - timer.startTime;
    this.timers.delete(operationId);

    // 记录延迟
    this.recordLatency(category, duration, timer.metadata);
    
    return duration;
  }

  /**
   * 记录延迟指标
   */
  recordLatency(category, duration, metadata = {}) {
    if (!this.metrics.latency[category]) {
      this.metrics.latency[category] = [];
    }

    this.metrics.latency[category].push({
      duration,
      timestamp: Date.now(),
      metadata
    });

    // 保持历史记录大小
    if (this.metrics.latency[category].length > this.options.historySize) {
      this.metrics.latency[category].shift();
    }

    // 发出延迟事件
    this.emit('latencyRecorded', { category, duration, metadata });
  }

  /**
   * 记录错误
   */
  recordError(category = 'total', error = null) {
    this.metrics.errors.total++;
    
    if (this.metrics.errors[category] !== undefined) {
      this.metrics.errors[category]++;
    }

    // 计算错误率
    const totalOperations = this.metrics.throughput.messages + this.metrics.errors.total;
    this.metrics.errors.rate = totalOperations > 0 ? this.metrics.errors.total / totalOperations : 0;

    this.emit('errorRecorded', { category, error, total: this.metrics.errors.total });
  }

  /**
   * 记录吞吐量
   */
  recordThroughput(type, count = 1, bytes = 0) {
    if (this.metrics.throughput[type] !== undefined) {
      this.metrics.throughput[type] += count;
    }
    
    if (bytes > 0) {
      this.metrics.throughput.bytesTransferred += bytes;
    }

    this.emit('throughputRecorded', { type, count, bytes });
  }

  /**
   * 更新连接指标
   */
  updateConnectionMetrics(active, total, peak, averageLifetime) {
    this.metrics.connections = {
      active,
      total,
      peak: Math.max(peak, this.metrics.connections.peak),
      averageLifetime
    };
  }

  /**
   * 收集系统指标
   */
  collectMetrics() {
    // 内存使用情况
    const memUsage = process.memoryUsage();
    this.metrics.system.memory = {
      used: memUsage.heapUsed,
      total: memUsage.heapTotal,
      percentage: memUsage.heapUsed / memUsage.heapTotal
    };

    // 运行时间
    this.metrics.system.uptime = Date.now() - this.startTime;

    // CPU使用率（简化版本，实际应用中可以使用更精确的方法）
    const cpuUsage = process.cpuUsage();
    this.metrics.system.cpu.percentage = (cpuUsage.user + cpuUsage.system) / 1000000; // 转换为秒

    this.emit('metricsCollected', this.metrics);
  }

  /**
   * 检查告警阈值
   */
  checkAlerts() {
    const alerts = [];

    // 检查延迟告警
    for (const [category, latencies] of Object.entries(this.metrics.latency)) {
      if (latencies.length > 0) {
        const avgLatency = this.calculateAverageLatency(category);
        if (avgLatency > this.options.alertThresholds.latency) {
          alerts.push({
            type: 'latency',
            category,
            value: avgLatency,
            threshold: this.options.alertThresholds.latency,
            message: `High latency detected in ${category}: ${avgLatency}ms`
          });
        }
      }
    }

    // 检查内存使用告警
    if (this.metrics.system.memory.percentage > this.options.alertThresholds.memoryUsage) {
      alerts.push({
        type: 'memory',
        value: this.metrics.system.memory.percentage,
        threshold: this.options.alertThresholds.memoryUsage,
        message: `High memory usage: ${(this.metrics.system.memory.percentage * 100).toFixed(1)}%`
      });
    }

    // 检查错误率告警
    if (this.metrics.errors.rate > this.options.alertThresholds.errorRate) {
      alerts.push({
        type: 'errorRate',
        value: this.metrics.errors.rate,
        threshold: this.options.alertThresholds.errorRate,
        message: `High error rate: ${(this.metrics.errors.rate * 100).toFixed(1)}%`
      });
    }

    // 发出告警事件
    if (alerts.length > 0) {
      this.emit('alerts', alerts);
      console.warn('Performance alerts:', alerts);
    }
  }

  /**
   * 保存性能快照
   */
  saveSnapshot() {
    const snapshot = {
      timestamp: Date.now(),
      metrics: JSON.parse(JSON.stringify(this.metrics)),
      summary: this.generateSummary()
    };

    this.history.push(snapshot);

    // 保持历史记录大小
    if (this.history.length > this.options.historySize) {
      this.history.shift();
    }

    this.emit('snapshotSaved', snapshot);
  }

  /**
   * 生成性能摘要
   */
  generateSummary() {
    return {
      averageLatency: {
        ai: this.calculateAverageLatency('ai'),
        tts: this.calculateAverageLatency('tts'),
        audio: this.calculateAverageLatency('audio'),
        total: this.calculateAverageLatency('total')
      },
      throughputPerSecond: {
        messages: this.calculateThroughputPerSecond('messages'),
        audioChunks: this.calculateThroughputPerSecond('audioChunks'),
        bytesTransferred: this.calculateThroughputPerSecond('bytesTransferred')
      },
      errorRate: this.metrics.errors.rate,
      memoryUsage: this.metrics.system.memory.percentage,
      activeConnections: this.metrics.connections.active,
      uptime: this.metrics.system.uptime
    };
  }

  /**
   * 计算平均延迟
   */
  calculateAverageLatency(category) {
    const latencies = this.metrics.latency[category];
    if (!latencies || latencies.length === 0) return 0;

    const sum = latencies.reduce((total, item) => total + item.duration, 0);
    return Math.round(sum / latencies.length);
  }

  /**
   * 计算每秒吞吐量
   */
  calculateThroughputPerSecond(type) {
    const value = this.metrics.throughput[type] || 0;
    const uptimeSeconds = this.metrics.system.uptime / 1000;
    return uptimeSeconds > 0 ? Math.round(value / uptimeSeconds) : 0;
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    return {
      current: this.metrics,
      summary: this.generateSummary(),
      history: this.history.slice(-10), // 最近10个快照
      alerts: this.getActiveAlerts()
    };
  }

  /**
   * 获取活跃告警
   */
  getActiveAlerts() {
    // 这里可以实现更复杂的告警状态管理
    return [];
  }

  /**
   * 重置指标
   */
  resetMetrics() {
    // 重置计数器，但保留历史数据
    this.metrics.throughput = {
      messages: 0,
      audioChunks: 0,
      bytesTransferred: 0,
      connectionsPerSecond: 0
    };

    this.metrics.errors = {
      total: 0,
      ai: 0,
      tts: 0,
      connection: 0,
      rate: 0
    };

    console.log('Performance metrics reset');
  }

  /**
   * 导出性能数据
   */
  exportData(format = 'json') {
    const data = {
      exportTime: Date.now(),
      metrics: this.metrics,
      history: this.history,
      summary: this.generateSummary()
    };

    if (format === 'json') {
      return JSON.stringify(data, null, 2);
    } else if (format === 'csv') {
      // 简化的CSV导出
      return this.convertToCSV(data);
    } else {
      throw new Error(`Unsupported export format: ${format}`);
    }
  }

  /**
   * 转换为CSV格式
   */
  convertToCSV(data) {
    const headers = ['timestamp', 'ai_latency', 'tts_latency', 'audio_latency', 'memory_usage', 'error_rate'];
    const rows = [headers.join(',')];

    for (const snapshot of data.history) {
      const row = [
        snapshot.timestamp,
        snapshot.summary.averageLatency.ai,
        snapshot.summary.averageLatency.tts,
        snapshot.summary.averageLatency.audio,
        (snapshot.metrics.system.memory.percentage * 100).toFixed(2),
        (snapshot.metrics.errors.rate * 100).toFixed(2)
      ];
      rows.push(row.join(','));
    }

    return rows.join('\n');
  }
}

module.exports = PerformanceMonitor;
