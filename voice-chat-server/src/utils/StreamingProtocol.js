const zlib = require("zlib");
const { promisify } = require("util");

const gzip = promisify(zlib.gzip);
const gunzip = promisify(zlib.gunzip);

/**
 * 增强的流式数据传输协议
 * 支持数据压缩、分片传输、流控制等功能
 */
class StreamingProtocol {
  constructor(options = {}) {
    this.options = {
      enableCompression: options.enableCompression !== false,
      compressionThreshold: options.compressionThreshold || 1024, // 1KB
      maxChunkSize: options.maxChunkSize || 8192, // 8KB
      adaptiveChunking: options.adaptiveChunking !== false,
      flowControlWindow: options.flowControlWindow || 64 * 1024, // 64KB
      ...options,
    };

    // 流控制状态
    this.flowControlStates = new Map(); // connectionId -> state

    console.log("StreamingProtocol initialized with options:", this.options);
  }

  /**
   * 初始化连接的流控制状态
   */
  initFlowControl(connectionId) {
    this.flowControlStates.set(connectionId, {
      windowSize: this.options.flowControlWindow,
      bytesInFlight: 0,
      pendingChunks: [],
      lastAckTime: Date.now(),
      rtt: 100, // 初始RTT估值
      bandwidth: 1024 * 1024, // 初始带宽估值 1MB/s
    });
  }

  /**
   * 清理连接的流控制状态
   */
  cleanupFlowControl(connectionId) {
    this.flowControlStates.delete(connectionId);
  }

  /**
   * 准备发送数据 - 支持压缩和分片
   */
  async prepareData(data, options = {}) {
    const {
      type = "json",
      enableCompression = this.options.enableCompression,
      chunkSize = this.options.maxChunkSize,
    } = options;

    let payload;
    let contentType;

    // 序列化数据
    if (type === "json") {
      payload = Buffer.from(JSON.stringify(data), "utf8");
      contentType = "application/json";
    } else if (type === "binary") {
      payload = Buffer.isBuffer(data) ? data : Buffer.from(data);
      contentType = "application/octet-stream";
    } else if (type === "text") {
      payload = Buffer.from(data, "utf8");
      contentType = "text/plain";
    } else {
      throw new Error(`Unsupported data type: ${type}`);
    }

    // 压缩处理
    let compressed = false;
    if (
      enableCompression &&
      payload.length >= this.options.compressionThreshold
    ) {
      try {
        payload = await gzip(payload);
        compressed = true;
      } catch (error) {
        console.warn(
          "Compression failed, sending uncompressed:",
          error.message
        );
      }
    }

    // 分片处理
    const chunks = this.createChunks(payload, chunkSize);

    return {
      chunks,
      metadata: {
        originalSize: payload.length,
        compressed,
        contentType,
        totalChunks: chunks.length,
        timestamp: Date.now(),
      },
    };
  }

  /**
   * 创建数据分片
   */
  createChunks(buffer, chunkSize) {
    const chunks = [];
    let offset = 0;

    while (offset < buffer.length) {
      const end = Math.min(offset + chunkSize, buffer.length);
      const chunk = buffer.slice(offset, end);

      chunks.push({
        data: chunk,
        index: chunks.length,
        offset,
        size: chunk.length,
        isLast: end === buffer.length,
      });

      offset = end;
    }

    return chunks;
  }

  /**
   * 发送流式数据
   */
  async sendStreamData(ws, data, options = {}) {
    const connectionId = ws.connectionId;
    if (!connectionId) {
      throw new Error("Connection ID not found");
    }

    // 初始化流控制状态（如果需要）
    if (!this.flowControlStates.has(connectionId)) {
      this.initFlowControl(connectionId);
    }

    const flowState = this.flowControlStates.get(connectionId);
    const { chunks, metadata } = await this.prepareData(data, options);

    // 发送元数据
    const metadataMessage = {
      type: "stream_metadata",
      metadata,
      streamId: this.generateStreamId(),
    };

    this.sendMessage(ws, metadataMessage);

    // 流控制发送分片
    for (const chunk of chunks) {
      await this.sendChunkWithFlowControl(ws, chunk, metadata, flowState);
    }

    // 发送完成信号
    const completeMessage = {
      type: "stream_complete",
      streamId: metadataMessage.streamId,
      totalBytes: metadata.originalSize,
    };

    this.sendMessage(ws, completeMessage);
  }

  /**
   * 带流控制的分片发送
   */
  async sendChunkWithFlowControl(ws, chunk, metadata, flowState) {
    // 检查流控制窗口
    while (flowState.bytesInFlight >= flowState.windowSize) {
      await this.waitForWindowUpdate(flowState);
    }

    const chunkMessage = {
      type: "stream_chunk",
      chunk: {
        index: chunk.index,
        data: chunk.data.toString("base64"),
        size: chunk.size,
        isLast: chunk.isLast,
      },
      metadata: {
        compressed: metadata.compressed,
        totalChunks: metadata.totalChunks,
      },
    };

    this.sendMessage(ws, chunkMessage);
    flowState.bytesInFlight += chunk.size;

    // 自适应分片大小调整
    if (this.options.adaptiveChunking) {
      this.adjustChunkSize(flowState);
    }
  }

  /**
   * 等待流控制窗口更新
   */
  async waitForWindowUpdate(flowState, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();

      const checkWindow = () => {
        if (flowState.bytesInFlight < flowState.windowSize) {
          resolve();
        } else if (Date.now() - startTime > timeout) {
          reject(new Error("Flow control timeout"));
        } else {
          setTimeout(checkWindow, 10);
        }
      };

      checkWindow();
    });
  }

  /**
   * 处理流控制确认
   */
  handleFlowControlAck(connectionId, bytesAcked) {
    const flowState = this.flowControlStates.get(connectionId);
    if (!flowState) return;

    flowState.bytesInFlight = Math.max(0, flowState.bytesInFlight - bytesAcked);
    flowState.lastAckTime = Date.now();

    // 更新RTT估算
    this.updateRttEstimate(flowState);
  }

  /**
   * 更新RTT估算
   */
  updateRttEstimate(flowState) {
    const now = Date.now();
    const rtt = now - flowState.lastAckTime;

    // 简单的RTT平滑算法
    flowState.rtt = flowState.rtt * 0.875 + rtt * 0.125;
  }

  /**
   * 自适应分片大小调整
   */
  adjustChunkSize(flowState) {
    const { rtt, bandwidth } = flowState;

    // 基于RTT和带宽调整分片大小
    const optimalChunkSize = Math.min(
      Math.max((bandwidth * rtt) / 1000, 1024), // 最小1KB
      this.options.maxChunkSize // 最大限制
    );

    flowState.adaptiveChunkSize = optimalChunkSize;
  }

  /**
   * 发送消息
   */
  sendMessage(ws, message) {
    if (ws.readyState === ws.OPEN) {
      try {
        ws.send(JSON.stringify(message));
        return true;
      } catch (error) {
        console.error("Failed to send message:", error);
        return false;
      }
    }
    return false;
  }

  /**
   * 接收和重组流式数据
   */
  async receiveStreamData(chunks, metadata) {
    // 按索引排序分片
    chunks.sort((a, b) => a.index - b.index);

    // 重组数据
    const buffers = chunks.map((chunk) => Buffer.from(chunk.data, "base64"));
    let reassembled = Buffer.concat(buffers);

    // 解压缩
    if (metadata.compressed) {
      try {
        reassembled = await gunzip(reassembled);
      } catch (error) {
        throw new Error(`Decompression failed: ${error.message}`);
      }
    }

    // 反序列化
    if (metadata.contentType === "application/json") {
      return JSON.parse(reassembled.toString("utf8"));
    } else if (metadata.contentType === "text/plain") {
      return reassembled.toString("utf8");
    } else {
      return reassembled;
    }
  }

  /**
   * 创建音频流消息
   */
  createAudioStreamMessage(audioData, options = {}) {
    const {
      format = "mp3",
      sampleRate = 24000,
      channels = 1,
      sequenceNumber = 1,
      totalChunks = 1,
      sentenceIndex = null,
      isLast = false,
    } = options;

    return {
      type: "audio_stream",
      data: audioData.toString("base64"),
      metadata: {
        format,
        sampleRate,
        channels,
        sequenceNumber,
        totalChunks,
        sentenceIndex,
        chunkIndex: options.chunkIndex,
        isLast,
        isFinal: options.isFinal,
        isImmediate: options.isImmediate,
      },
      size: audioData.length,
      timestamp: Date.now(),
    };
  }

  /**
   * 创建文本流消息
   */
  createTextStreamMessage(content, options = {}) {
    const {
      type = "ai_text_stream",
      sentenceIndex = null,
      isComplete = false,
    } = options;

    return {
      type,
      content,
      sentenceIndex,
      isComplete,
      timestamp: Date.now(),
    };
  }

  /**
   * 生成流ID
   */
  generateStreamId() {
    return `stream_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取流控制统计
   */
  getFlowControlStats(connectionId) {
    const flowState = this.flowControlStates.get(connectionId);
    if (!flowState) return null;

    return {
      windowSize: flowState.windowSize,
      bytesInFlight: flowState.bytesInFlight,
      rtt: flowState.rtt,
      bandwidth: flowState.bandwidth,
      adaptiveChunkSize:
        flowState.adaptiveChunkSize || this.options.maxChunkSize,
    };
  }

  /**
   * 重置流控制状态
   */
  resetFlowControl(connectionId) {
    const flowState = this.flowControlStates.get(connectionId);
    if (flowState) {
      flowState.bytesInFlight = 0;
      flowState.pendingChunks = [];
      flowState.lastAckTime = Date.now();
    }
  }
}

module.exports = StreamingProtocol;
