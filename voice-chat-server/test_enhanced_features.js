const WebSocket = require('ws');

/**
 * 测试增强功能的脚本
 */
class EnhancedFeatureTest {
  constructor() {
    this.ws = null;
    this.testResults = {
      connection: false,
      ping: false,
      chat: false,
      performance: false,
      flowControl: false
    };
  }

  async runTests() {
    console.log('🧪 开始测试增强功能...\n');

    try {
      await this.testConnection();
      await this.testPing();
      await this.testPerformanceReport();
      await this.testFlowControl();
      
      this.printResults();
    } catch (error) {
      console.error('❌ 测试过程中出现错误:', error);
    } finally {
      if (this.ws) {
        this.ws.close();
      }
    }
  }

  async testConnection() {
    console.log('1️⃣ 测试WebSocket连接管理...');
    
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket('ws://localhost:3000');
      
      this.ws.on('open', () => {
        console.log('✅ WebSocket连接建立成功');
        this.testResults.connection = true;
        resolve();
      });

      this.ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          if (message.type === 'connected') {
            console.log(`✅ 收到欢迎消息: ${message.message}`);
            if (message.serverCapabilities) {
              console.log(`✅ 服务器能力: ${JSON.stringify(message.serverCapabilities)}`);
            }
            if (message.connectionId) {
              console.log(`✅ 连接ID: ${message.connectionId}`);
            }
          }
        } catch (error) {
          console.warn('解析消息失败:', error);
        }
      });

      this.ws.on('error', (error) => {
        console.error('❌ WebSocket连接错误:', error);
        reject(error);
      });

      setTimeout(() => {
        if (!this.testResults.connection) {
          reject(new Error('连接超时'));
        }
      }, 5000);
    });
  }

  async testPing() {
    console.log('\n2️⃣ 测试增强的心跳机制...');
    
    return new Promise((resolve) => {
      const pingTime = Date.now();
      
      const messageHandler = (data) => {
        try {
          const message = JSON.parse(data);
          if (message.type === 'pong') {
            const latency = Date.now() - pingTime;
            console.log(`✅ 收到pong响应，延迟: ${latency}ms`);
            if (message.latency) {
              console.log(`✅ 服务器计算的延迟: ${message.latency}ms`);
            }
            this.testResults.ping = true;
            this.ws.removeListener('message', messageHandler);
            resolve();
          }
        } catch (error) {
          console.warn('解析pong消息失败:', error);
        }
      };

      this.ws.on('message', messageHandler);
      
      this.ws.send(JSON.stringify({
        type: 'ping',
        timestamp: pingTime
      }));

      setTimeout(() => {
        this.ws.removeListener('message', messageHandler);
        if (!this.testResults.ping) {
          console.log('⚠️ ping测试超时');
        }
        resolve();
      }, 3000);
    });
  }

  async testPerformanceReport() {
    console.log('\n3️⃣ 测试性能监控报告...');
    
    return new Promise((resolve) => {
      const messageHandler = (data) => {
        try {
          const message = JSON.parse(data);
          if (message.type === 'performance_report') {
            console.log('✅ 收到性能报告');
            console.log(`✅ 活跃连接: ${message.connection.activeConnections}`);
            console.log(`✅ 平均延迟: ${message.connection.averageLatency}ms`);
            console.log(`✅ 内存使用: ${(message.performance.summary.memoryUsage * 100).toFixed(1)}%`);
            
            if (message.flowControl) {
              console.log(`✅ 流控制窗口: ${message.flowControl.windowSize} bytes`);
            }
            
            this.testResults.performance = true;
            this.ws.removeListener('message', messageHandler);
            resolve();
          }
        } catch (error) {
          console.warn('解析性能报告失败:', error);
        }
      };

      this.ws.on('message', messageHandler);
      
      this.ws.send(JSON.stringify({
        type: 'performance_request',
        timestamp: Date.now()
      }));

      setTimeout(() => {
        this.ws.removeListener('message', messageHandler);
        if (!this.testResults.performance) {
          console.log('⚠️ 性能报告测试超时');
        }
        resolve();
      }, 5000);
    });
  }

  async testFlowControl() {
    console.log('\n4️⃣ 测试流控制机制...');
    
    // 发送流控制确认消息
    this.ws.send(JSON.stringify({
      type: 'flow_control_ack',
      bytesAcked: 1024,
      timestamp: Date.now()
    }));
    
    console.log('✅ 发送流控制确认');
    this.testResults.flowControl = true;
    
    return Promise.resolve();
  }

  printResults() {
    console.log('\n📊 测试结果汇总:');
    console.log('==================');
    
    Object.entries(this.testResults).forEach(([test, result]) => {
      const status = result ? '✅ 通过' : '❌ 失败';
      const testName = {
        connection: 'WebSocket连接管理',
        ping: '增强心跳机制',
        chat: 'AI流式对话+TTS',
        performance: '性能监控报告',
        flowControl: '流控制机制'
      }[test];
      
      console.log(`${testName}: ${status}`);
    });
    
    const passedTests = Object.values(this.testResults).filter(Boolean).length;
    const totalTests = Object.keys(this.testResults).length;
    
    console.log('==================');
    console.log(`总体结果: ${passedTests}/${totalTests} 测试通过`);
    
    if (passedTests === totalTests) {
      console.log('🎉 所有增强功能测试通过！');
    } else {
      console.log('⚠️ 部分功能需要检查');
    }
  }
}

// 运行测试
if (require.main === module) {
  const test = new EnhancedFeatureTest();
  test.runTests().catch(console.error);
}

module.exports = EnhancedFeatureTest;
