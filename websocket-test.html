<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 长连接与流式传输测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2196F3, #21CBF3);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .status-bar {
            display: flex;
            justify-content: space-around;
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .status-item {
            text-align: center;
        }

        .status-label {
            font-size: 0.9em;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .status-value {
            font-size: 1.2em;
            font-weight: bold;
            padding: 5px 15px;
            border-radius: 20px;
        }

        .status-connected {
            background: #28a745;
            color: white;
        }

        .status-disconnected {
            background: #dc3545;
            color: white;
        }

        .status-connecting {
            background: #ffc107;
            color: #212529;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 30px;
        }

        .panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border: 1px solid #dee2e6;
        }

        .panel h3 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.3em;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #495057;
        }

        .input-field {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            font-size: 1em;
            transition: border-color 0.3s;
        }

        .input-field:focus {
            outline: none;
            border-color: #2196F3;
        }

        .btn {
            background: linear-gradient(135deg, #2196F3, #21CBF3);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 10px;
            font-size: 1em;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
        }

        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #e84463);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .message-area {
            height: 300px;
            overflow-y: auto;
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 10px;
            animation: fadeIn 0.3s ease-in;
        }

        .message-sent {
            background: #e3f2fd;
            border-left: 4px solid #2196F3;
        }

        .message-received {
            background: #f3e5f5;
            border-left: 4px solid #9c27b0;
        }

        .message-system {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
        }

        .message-error {
            background: #ffebee;
            border-left: 4px solid #f44336;
        }

        .message-time {
            font-size: 0.8em;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .audio-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-top: 15px;
        }

        .audio-player {
            flex: 1;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #dee2e6;
            border-radius: 4px;
            margin: 10px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #2196F3, #21CBF3);
            width: 0%;
            transition: width 0.3s ease;
        }

        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .metric-card {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #dee2e6;
        }

        .metric-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9em;
            color: #6c757d;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .typing-indicator {
            animation: blink 1s infinite;
            color: #2196F3;
            font-weight: bold;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .status-bar {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🚀 WebSocket 长连接与流式传输测试</h1>
            <p>测试避免HTTP握手开销和边生成边发送的流式音频传输</p>
        </header>

        <div class="status-bar">
            <div class="status-item">
                <div class="status-label">连接状态</div>
                <div id="connectionStatus" class="status-value status-disconnected">未连接</div>
            </div>
            <div class="status-item">
                <div class="status-label">消息计数</div>
                <div id="messageCount" class="status-value">0</div>
            </div>
            <div class="status-item">
                <div class="status-label">连接时长</div>
                <div id="connectionTime" class="status-value">00:00</div>
            </div>
            <div class="status-item">
                <div class="status-label">延迟</div>
                <div id="latency" class="status-value">-</div>
            </div>
        </div>

        <div class="main-content">
            <div class="panel">
                <h3>🔌 连接控制</h3>
                <div class="control-group">
                    <label for="serverUrl">服务器地址:</label>
                    <input type="text" id="serverUrl" class="input-field" value="ws://localhost:3000" placeholder="ws://localhost:3000">
                </div>
                <button id="connectBtn" class="btn">连接服务器</button>
                <button id="disconnectBtn" class="btn btn-danger" disabled>断开连接</button>
                <button id="pingBtn" class="btn" disabled>发送心跳</button>
                <button id="performanceBtn" class="btn" disabled>获取性能报告</button>
            </div>

            <div class="panel">
                <h3>💬 消息测试</h3>
                <div class="control-group">
                    <label for="messageInput">测试消息:</label>
                    <input type="text" id="messageInput" class="input-field" placeholder="输入测试消息..." value="">
                </div>
                <button id="sendChatBtn" class="btn" disabled>发送聊天消息</button>
                <button id="sendTtsBtn" class="btn btn-success" disabled>发送TTS请求</button>
                <button id="stopAudioBtn" class="btn btn-danger" disabled>🛑 停止播放</button>
                
                <div class="control-group" style="margin-top: 20px;">
                    <label>
                        <input type="checkbox" id="enableTts" checked> 启用TTS语音合成
                    </label>
                    <label>
                        <input type="checkbox" id="enableStreaming" checked> 启用流式传输
                    </label>
                    <label>
                        <input type="checkbox" id="enableCompression" checked> 启用数据压缩
                    </label>
                    <label>
                        <input type="checkbox" id="enableFlowControl" checked> 启用流控制
                    </label>
                </div>
            </div>
        </div>

        <div style="padding: 0 30px 30px 30px;">
            <div class="panel">
                <h3>📝 消息记录</h3>
                <div id="messageArea" class="message-area"></div>
                <button id="clearBtn" class="btn">清空记录</button>
            </div>
        </div>

        <div style="padding: 0 30px 30px 30px;">
            <div class="panel">
                <h3>🎵 音频播放控制</h3>
                <div class="audio-controls">
                    <audio id="audioPlayer" controls style="width: 100%;">
                        您的浏览器不支持音频播放。
                    </audio>
                </div>
                <div class="progress-bar">
                    <div id="audioProgress" class="progress-fill"></div>
                </div>
                <div id="audioStatus">等待音频数据...</div>
            </div>
        </div>

        <div style="padding: 0 30px 30px 30px;">
            <div class="panel">
                <h3>📊 性能指标</h3>
                <div class="metrics">
                    <div class="metric-card">
                        <div id="totalMessages" class="metric-value">0</div>
                        <div class="metric-label">总消息数</div>
                    </div>
                    <div class="metric-card">
                        <div id="avgLatency" class="metric-value">0ms</div>
                        <div class="metric-label">平均延迟</div>
                    </div>
                    <div class="metric-card">
                        <div id="audioChunks" class="metric-value">0</div>
                        <div class="metric-label">音频分片</div>
                    </div>
                    <div class="metric-card">
                        <div id="firstChunkTime" class="metric-value">0ms</div>
                        <div class="metric-label">首片延迟</div>
                    </div>
                    <div class="metric-card">
                        <div id="voiceLatency" class="metric-value">-</div>
                        <div class="metric-label">语音回复延迟</div>
                    </div>
                    <div class="metric-card">
                        <div id="avgVoiceLatency" class="metric-value">-</div>
                        <div class="metric-label">平均语音延迟</div>
                    </div>
                    <div class="metric-card">
                        <div id="compressionRatio" class="metric-value">-</div>
                        <div class="metric-label">压缩比</div>
                    </div>
                    <div class="metric-card">
                        <div id="flowControlWindow" class="metric-value">-</div>
                        <div class="metric-label">流控制窗口</div>
                    </div>
                </div>
            </div>
        </div>

        <div style="padding: 0 30px 30px 30px;">
            <div class="panel">
                <h3>📊 服务器性能监控</h3>
                <div id="performanceData" class="message-area" style="height: 200px;">
                    <div style="text-align: center; color: #6c757d; margin-top: 80px;">
                        点击"获取性能报告"按钮查看服务器性能数据
                    </div>
                </div>
                <button id="refreshPerformanceBtn" class="btn" disabled>刷新性能数据</button>
                <button id="exportPerformanceBtn" class="btn" disabled>导出性能数据</button>
            </div>
        </div>
    </div>

    <script>
        class WebSocketTester {
            constructor() {
                this.ws = null;
                this.messageCount = 0;
                this.connectTime = null;
                this.latencyHistory = [];
                this.audioChunks = 0;
                this.firstChunkTime = 0;
                this.currentAudioBlob = null;
                this.audioChunkBuffers = [];
                
                // 心跳延迟追踪
                this.pingTimestamp = null;
                
                // 语音回复延迟追踪
                this.voiceRequestTimestamp = null;
                this.voiceTextResponseTime = null;  // AI文本响应时间
                this.voiceAudioResponseTime = null; // 音频响应时间
                this.voiceResponseHistory = [];     // 存储历史语音延迟数据
                
                // AI流式文本处理
                this.currentAIResponse = "";
                this.aiResponseElement = null;
                
                // 句子级音频处理
                this.sentenceAudioBuffers = new Map(); // sentenceIndex -> [chunks]
                this.audioPlayQueue = []; // 待播放的音频队列
                this.isPlaying = false;
                
                this.initializeEventListeners();
                this.startConnectionTimer();
            }

            initializeEventListeners() {
                document.getElementById('connectBtn').addEventListener('click', () => this.connect());
                document.getElementById('disconnectBtn').addEventListener('click', () => this.disconnect());
                document.getElementById('pingBtn').addEventListener('click', () => this.sendPing());
                document.getElementById('sendChatBtn').addEventListener('click', () => this.sendChatMessage());
                document.getElementById('sendTtsBtn').addEventListener('click', () => this.sendTtsRequest());
                document.getElementById('stopAudioBtn').addEventListener('click', () => this.stopAudio());
                document.getElementById('clearBtn').addEventListener('click', () => this.clearMessages());
                document.getElementById('performanceBtn').addEventListener('click', () => this.requestPerformanceReport());
                document.getElementById('refreshPerformanceBtn').addEventListener('click', () => this.requestPerformanceReport());
                document.getElementById('exportPerformanceBtn').addEventListener('click', () => this.exportPerformanceData());

                document.getElementById('messageInput').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.sendChatMessage();
                    }
                });
            }

            connect() {
                const url = document.getElementById('serverUrl').value;
                this.updateConnectionStatus('连接中...', 'connecting');
                
                try {
                    this.ws = new WebSocket(url);
                    
                    this.ws.onopen = () => {
                        this.connectTime = Date.now();
                        this.updateConnectionStatus('已连接', 'connected');
                        this.addMessage('system', '✅ WebSocket连接已建立');
                        this.updateButtons(true);
                    };
                    
                    this.ws.onmessage = (event) => {
                        this.handleMessage(event.data);
                    };
                    
                    this.ws.onclose = () => {
                        this.updateConnectionStatus('未连接', 'disconnected');
                        this.addMessage('system', '❌ WebSocket连接已断开');
                        this.updateButtons(false);
                        this.connectTime = null;
                    };
                    
                    this.ws.onerror = (error) => {
                        this.addMessage('error', `❌ 连接错误: ${error.message || '未知错误'}`);
                        this.updateConnectionStatus('连接失败', 'disconnected');
                    };
                    
                } catch (error) {
                    this.addMessage('error', `❌ 连接失败: ${error.message}`);
                    this.updateConnectionStatus('连接失败', 'disconnected');
                }
            }

            disconnect() {
                if (this.ws) {
                    this.ws.close();
                }
            }

            sendPing() {
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    const pingTime = Date.now();
                    this.pingTimestamp = pingTime; // 记录ping时间戳
                    this.ws.send(JSON.stringify({
                        type: 'ping',
                        timestamp: pingTime
                    }));
                    this.addMessage('sent', '🏓 发送心跳包');
                }
            }

            sendChatMessage() {
                const message = document.getElementById('messageInput').value.trim();
                const enableTts = document.getElementById('enableTts').checked;
                
                if (!message) {
                    alert('请输入消息内容');
                    return;
                }
                
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    const data = {
                        type: 'chat',
                        content: message,
                        enableTts: enableTts,
                        audioFormat: 'mp3'
                    };
                    
                    // 如果启用TTS，记录请求时间
                    if (enableTts) {
                        this.voiceRequestTimestamp = Date.now();
                        this.voiceTextResponseTime = null;
                        this.voiceAudioResponseTime = null;
                    }
                    
                    this.ws.send(JSON.stringify(data));
                    this.addMessage('sent', `💬 ${message}`);
                    document.getElementById('messageInput').value = '';
                    
                    if (enableTts) {
                        this.resetAudioMetrics();
                    }
                } else {
                    alert('WebSocket未连接');
                }
            }

            sendTtsRequest() {
                const message = document.getElementById('messageInput').value.trim();
                const streaming = document.getElementById('enableStreaming').checked;

                if (!message) {
                    alert('请输入要转换的文本');
                    return;
                }

                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    const data = {
                        type: 'tts_request',
                        text: message,
                        format: 'mp3',
                        streaming: streaming
                    };

                    this.ws.send(JSON.stringify(data));
                    this.addMessage('sent', `🎵 TTS请求: ${message}`);
                    this.resetAudioMetrics();
                } else {
                    alert('WebSocket未连接');
                }
            }

            stopAudio() {
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    const data = {
                        type: 'stop_audio',
                        stopAll: true,
                        timestamp: Date.now()
                    };

                    this.ws.send(JSON.stringify(data));
                    this.addMessage('sent', '🛑 请求停止音频播放');

                    // 立即停止本地音频播放
                    this.stopCurrentAudio();
                } else {
                    alert('WebSocket未连接');
                }
            }

            stopCurrentAudio() {
                // 停止当前音频播放
                const audioPlayer = document.getElementById('audioPlayer');
                if (audioPlayer) {
                    audioPlayer.pause();
                    audioPlayer.currentTime = 0;
                    this.updateAudioStatus('音频播放已停止');
                }

                // 清空音频队列和缓冲区
                this.audioPlayQueue = [];
                this.audioChunkBuffers = [];
                this.sentenceAudioBuffers.clear();
                this.isPlaying = false;

                // 重置音频指标
                this.resetAudioMetrics();
            }

            handleMessage(data) {
                try {
                    const message = JSON.parse(data);
                    this.messageCount++;
                    document.getElementById('messageCount').textContent = this.messageCount;
                    
                    switch (message.type) {
                        case 'connected':
                            this.addMessage('received', `🎉 ${message.message}`);
                            if (message.serverCapabilities) {
                                this.addMessage('system', `🔧 服务器能力: ${JSON.stringify(message.serverCapabilities)}`);
                            }
                            if (message.connectionId) {
                                this.connectionId = message.connectionId;
                                this.addMessage('system', `🆔 连接ID: ${message.connectionId}`);
                            }
                            break;
                            
                        case 'pong':
                            if (this.pingTimestamp) {
                                const latency = Date.now() - this.pingTimestamp;
                                this.latencyHistory.push(latency);
                                this.addMessage('received', `🏓 心跳响应 (延迟: ${latency}ms)`);
                                this.updateLatencyDisplay();
                                
                                // 清理旧的历史记录（保留最近50条）
                                if (this.latencyHistory.length > 50) {
                                    this.latencyHistory = this.latencyHistory.slice(-50);
                                }
                                
                                this.pingTimestamp = null; // 清空时间戳
                            } else {
                                this.addMessage('received', '🏓 心跳响应');
                            }
                            break;
                            
                        case 'text_received':
                            this.addMessage('received', `📝 服务器收到: ${message.content}`);
                            break;
                            
                        case 'ai_start':
                            this.addMessage('received', `🤖 ${message.message}`);
                            
                            // 计算文本响应延迟
                            if (this.voiceRequestTimestamp) {
                                this.voiceTextResponseTime = Date.now() - this.voiceRequestTimestamp;
                                this.addMessage('system', `⏱️ 文本响应延迟: ${this.voiceTextResponseTime}ms`);
                            }
                            
                            this.startAIResponse();
                            break;
                            
                        case 'ai_text_stream':
                            this.handleAITextStream(message.content);
                            break;
                            
                        case 'ai_sentence_complete':
                            this.handleAISentenceComplete(message.sentence, message.sentenceIndex);
                            break;
                            
                        case 'ai_complete':
                            this.addMessage('received', `✅ AI回复完成 (${message.totalSentences}个句子)`);
                            this.completeAIResponse(message.fullResponse);
                            break;
                            
                        case 'ai_error':
                            this.addMessage('error', `❌ AI错误: ${message.error}`);
                            break;
                            
                        case 'sentence_tts_start':
                            this.addMessage('received', `🎵 开始句子TTS [${message.sentenceIndex}]: ${message.sentence}`);
                            
                            // 计算首批文字开始合成语音的延迟
                            if (message.sentenceIndex === 1 && this.voiceRequestTimestamp) {
                                const ttsStartDelay = Date.now() - this.voiceRequestTimestamp;
                                this.addMessage('system', `⏱️ 首批文字TTS开始延迟: ${ttsStartDelay}ms`);
                                
                                // 更新首片延迟显示（这是用户真正关心的指标）
                                if (!this.firstChunkTime || this.firstChunkTime === 0) {
                                    this.firstChunkTime = ttsStartDelay;
                                    document.getElementById('firstChunkTime').textContent = `${this.firstChunkTime}ms`;
                                    
                                    // 记录到延迟历史
                                    this.latencyHistory.push(this.firstChunkTime);
                                    this.updateLatencyDisplay();
                                }
                            }
                            break;
                            
                        case 'sentence_audio_chunk':
                            this.handleSentenceAudioChunk(message);
                            break;
                            
                        case 'sentence_tts_complete':
                            this.addMessage('received', `✅ 句子TTS完成 [${message.sentenceIndex}] (耗时: ${message.totalTime}ms)`);
                            this.playSentenceAudio(message.sentenceIndex);
                            break;
                            
                        case 'sentence_tts_error':
                            this.addMessage('error', `❌ 句子TTS错误 [${message.sentenceIndex}]: ${message.error}`);
                            break;

                        case 'partial_tts_start':
                            this.addMessage('received', `🚀 开始实时TTS [${message.sentenceIndex}]: ${message.sentence}`);
                            break;

                        case 'partial_tts_complete':
                            this.addMessage('received', `🚀 实时TTS完成 [${message.sentenceIndex}] (耗时: ${message.totalTime}ms)`);
                            break;

                        case 'partial_tts_error':
                            this.addMessage('error', `🚀 实时TTS错误 [${message.sentenceIndex}]: ${message.error}`);
                            break;

                        case 'audio_stopped':
                            this.addMessage('system', `🛑 ${message.message} (取消了${message.cancelledTasks}个任务)`);
                            this.stopCurrentAudio();
                            break;

                        case 'performance_report':
                            this.handlePerformanceReport(message);
                            break;

                        case 'flow_control_ack':
                            // 处理流控制确认
                            this.handleFlowControlAck(message);
                            break;

                        case 'text_response':
                            this.addMessage('received', `🤖 AI回复: ${message.content}`);
                            break;
                            
                        case 'tts_start':
                            this.addMessage('received', `🎵 开始TTS合成: ${message.text}`);
                            this.audioChunkBuffers = [];
                            break;
                            
                        case 'audio_chunk':
                            this.handleAudioChunk(message);
                            break;
                            
                        case 'tts_complete':
                            this.addMessage('received', `✅ TTS完成 (耗时: ${message.totalTime}ms, 分片: ${message.totalChunks})`);
                            this.playAudioFromChunks();
                            break;
                            
                        case 'audio_complete':
                            this.addMessage('received', `🎵 音频传输完成 (耗时: ${message.totalTime}ms)`);
                            this.playAudioFromBase64(message.data);
                            break;
                            
                        case 'error':
                        case 'tts_error':
                            this.addMessage('error', `❌ 错误: ${message.message || message.error}`);
                            break;
                            
                        default:
                            this.addMessage('received', `📨 ${JSON.stringify(message)}`);
                    }
                } catch (error) {
                    this.addMessage('error', `❌ 消息解析错误: ${error.message}`);
                }
            }

            handleAudioChunk(message) {
                this.audioChunks++;
                
                // 计算首片延迟（从请求发送到收到第一个音频分片）
                if (this.audioChunks === 1 && this.voiceRequestTimestamp) {
                    this.firstChunkTime = Date.now() - this.voiceRequestTimestamp;
                    document.getElementById('firstChunkTime').textContent = `${this.firstChunkTime}ms`;
                    
                    // 记录到延迟历史中
                    this.latencyHistory.push(this.firstChunkTime);
                    this.updateLatencyDisplay();
                    
                    // 清理旧的历史记录（保留最近50条）
                    if (this.latencyHistory.length > 50) {
                        this.latencyHistory = this.latencyHistory.slice(-50);
                    }
                } else if (this.audioChunks === 1) {
                    // 如果没有请求时间戳，显示"首片接收"
                    document.getElementById('firstChunkTime').textContent = '首片接收';
                }
                
                // 将base64音频数据转换为ArrayBuffer
                const audioData = atob(message.data);
                const audioArray = new Uint8Array(audioData.length);
                for (let i = 0; i < audioData.length; i++) {
                    audioArray[i] = audioData.charCodeAt(i);
                }
                
                this.audioChunkBuffers.push(audioArray);
                
                document.getElementById('audioChunks').textContent = this.audioChunks;
                this.addMessage('received', `🎵 音频分片 ${message.sequenceNumber}/${message.totalChunks || '?'} (${audioArray.length} bytes)`);
                
                // 如果是流式传输，可以立即播放
                if (message.sequenceNumber === 1) {
                    this.updateAudioStatus('开始播放音频流...');
                }
            }

            playAudioFromChunks() {
                if (this.audioChunkBuffers.length === 0) return;
                
                // 合并所有音频块
                const totalLength = this.audioChunkBuffers.reduce((sum, chunk) => sum + chunk.length, 0);
                const mergedArray = new Uint8Array(totalLength);
                let offset = 0;
                
                for (const chunk of this.audioChunkBuffers) {
                    mergedArray.set(chunk, offset);
                    offset += chunk.length;
                }
                
                // 创建Blob并播放
                const audioBlob = new Blob([mergedArray], { type: 'audio/mpeg' });
                this.playAudioBlob(audioBlob);
            }

            playAudioFromBase64(base64Data) {
                const audioData = atob(base64Data);
                const audioArray = new Uint8Array(audioData.length);
                for (let i = 0; i < audioData.length; i++) {
                    audioArray[i] = audioData.charCodeAt(i);
                }
                
                const audioBlob = new Blob([audioArray], { type: 'audio/mpeg' });
                this.playAudioBlob(audioBlob);
            }

            playAudioBlob(blob) {
                const audioPlayer = document.getElementById('audioPlayer');
                const audioUrl = URL.createObjectURL(blob);
                
                audioPlayer.src = audioUrl;
                audioPlayer.load();
                
                audioPlayer.addEventListener('loadeddata', () => {
                    this.updateAudioStatus(`音频已加载 (${(blob.size / 1024).toFixed(2)} KB)`);
                    audioPlayer.play().catch(e => {
                        this.updateAudioStatus(`播放失败: ${e.message}`);
                    });
                });
                
                audioPlayer.addEventListener('ended', () => {
                    URL.revokeObjectURL(audioUrl);
                    this.updateAudioStatus('播放完成');
                });
            }

            resetAudioMetrics() {
                this.audioChunks = 0;
                this.firstChunkTime = 0;
                this.audioChunkBuffers = [];
                document.getElementById('audioChunks').textContent = '0';
                document.getElementById('firstChunkTime').textContent = '0ms';
                this.updateAudioStatus('等待音频数据...');
            }

            updateAudioStatus(status) {
                document.getElementById('audioStatus').textContent = status;
            }

            addMessage(type, content) {
                const messageArea = document.getElementById('messageArea');
                const messageDiv = document.createElement('div');
                messageDiv.className = `message message-${type}`;
                
                const timeDiv = document.createElement('div');
                timeDiv.className = 'message-time';
                timeDiv.textContent = new Date().toLocaleTimeString();
                
                const contentDiv = document.createElement('div');
                contentDiv.textContent = content;
                
                messageDiv.appendChild(timeDiv);
                messageDiv.appendChild(contentDiv);
                messageArea.appendChild(messageDiv);
                messageArea.scrollTop = messageArea.scrollHeight;
                
                // 更新总消息数
                document.getElementById('totalMessages').textContent = document.querySelectorAll('.message').length;
            }

            clearMessages() {
                document.getElementById('messageArea').innerHTML = '';
                document.getElementById('totalMessages').textContent = '0';
            }

            updateConnectionStatus(status, type) {
                const statusElement = document.getElementById('connectionStatus');
                statusElement.textContent = status;
                statusElement.className = `status-value status-${type}`;
            }

            updateButtons(connected) {
                document.getElementById('connectBtn').disabled = connected;
                document.getElementById('disconnectBtn').disabled = !connected;
                document.getElementById('pingBtn').disabled = !connected;
                document.getElementById('sendChatBtn').disabled = !connected;
                document.getElementById('sendTtsBtn').disabled = !connected;
                document.getElementById('stopAudioBtn').disabled = !connected;
                document.getElementById('performanceBtn').disabled = !connected;
                document.getElementById('refreshPerformanceBtn').disabled = !connected;
                document.getElementById('exportPerformanceBtn').disabled = !connected;
            }

            updateLatencyDisplay() {
                if (this.latencyHistory.length > 0) {
                    const current = this.latencyHistory[this.latencyHistory.length - 1];
                    const average = this.latencyHistory.reduce((a, b) => a + b, 0) / this.latencyHistory.length;
                    
                    document.getElementById('latency').textContent = `${current}ms`;
                    document.getElementById('avgLatency').textContent = `${Math.round(average)}ms`;
                    
                    // 调试信息
                    console.log(`延迟更新 - 当前: ${current}ms, 平均: ${Math.round(average)}ms, 历史记录数: ${this.latencyHistory.length}`);
                } else {
                    // 没有延迟数据时的默认显示
                    document.getElementById('latency').textContent = '-';
                    document.getElementById('avgLatency').textContent = '0ms';
                    console.log('延迟更新 - 无历史数据');
                }
            }

            startConnectionTimer() {
                setInterval(() => {
                    if (this.connectTime) {
                        const elapsed = Math.floor((Date.now() - this.connectTime) / 1000);
                        const minutes = Math.floor(elapsed / 60);
                        const seconds = elapsed % 60;
                        document.getElementById('connectionTime').textContent = 
                            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                    }
                }, 1000);
            }

            // AI流式文本处理方法
            startAIResponse() {
                this.currentAIResponse = "";
                // 创建一个实时更新的AI回复元素
                const messageArea = document.getElementById('messageArea');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message message-received';
                
                const timeDiv = document.createElement('div');
                timeDiv.className = 'message-time';
                timeDiv.textContent = new Date().toLocaleTimeString();
                
                const contentDiv = document.createElement('div');
                contentDiv.innerHTML = '🤖 AI回复中: <span id="aiResponseText"></span><span class="typing-indicator">▋</span>';
                
                messageDiv.appendChild(timeDiv);
                messageDiv.appendChild(contentDiv);
                messageArea.appendChild(messageDiv);
                messageArea.scrollTop = messageArea.scrollHeight;
                
                this.aiResponseElement = messageDiv;
            }

            handleAITextStream(content) {
                this.currentAIResponse += content;
                if (this.aiResponseElement) {
                    const textSpan = this.aiResponseElement.querySelector('#aiResponseText');
                    if (textSpan) {
                        textSpan.textContent = this.currentAIResponse;
                    }
                }
            }

            handleAISentenceComplete(sentence, sentenceIndex) {
                this.addMessage('received', `📝 完整句子 [${sentenceIndex}]: ${sentence}`);
                
                // 初始化句子音频缓冲区
                this.sentenceAudioBuffers.set(sentenceIndex, []);
            }

            completeAIResponse(fullResponse) {
                if (this.aiResponseElement) {
                    // 移除打字指示器，显示完整回复
                    const contentDiv = this.aiResponseElement.querySelector('div:last-child');
                    if (contentDiv) {
                        contentDiv.innerHTML = `🤖 AI回复: ${fullResponse}`;
                    }
                    this.aiResponseElement = null;
                }
                this.currentAIResponse = "";
                
                // 更新总消息数
                document.getElementById('totalMessages').textContent = document.querySelectorAll('.message').length;
            }

            // 句子级音频处理方法
            handleSentenceAudioChunk(message) {
                const sentenceIndex = message.sentenceIndex;
                
                // 如果是第一个句子的第一个音频分片，计算首片音频延迟
                if (sentenceIndex === 1 && message.sequenceNumber === 1 && this.voiceRequestTimestamp) {
                    // 计算音频响应延迟（从请求到首片音频）
                    this.voiceAudioResponseTime = Date.now() - this.voiceRequestTimestamp;
                    this.addMessage('system', `⏱️ 音频响应延迟: ${this.voiceAudioResponseTime}ms`);
                    
                    // 更新首片延迟显示
                    this.firstChunkTime = this.voiceAudioResponseTime;
                    document.getElementById('firstChunkTime').textContent = `${this.firstChunkTime}ms`;
                    
                    // 记录到延迟历史中
                    this.latencyHistory.push(this.firstChunkTime);
                    this.updateLatencyDisplay();
                    
                    // 记录到语音响应历史数据
                    this.voiceResponseHistory.push({
                        textDelay: this.voiceTextResponseTime,
                        audioDelay: this.voiceAudioResponseTime,
                        timestamp: Date.now()
                    });
                    
                    // 更新语音延迟显示
                    this.updateVoiceLatencyDisplay();
                    
                    // 清理旧的历史记录（保留最近50条）
                    if (this.voiceResponseHistory.length > 50) {
                        this.voiceResponseHistory = this.voiceResponseHistory.slice(-50);
                    }
                    if (this.latencyHistory.length > 50) {
                        this.latencyHistory = this.latencyHistory.slice(-50);
                    }
                }
                
                if (!this.sentenceAudioBuffers.has(sentenceIndex)) {
                    this.sentenceAudioBuffers.set(sentenceIndex, []);
                }
                
                // 将base64音频数据转换为ArrayBuffer
                const audioData = atob(message.data);
                const audioArray = new Uint8Array(audioData.length);
                for (let i = 0; i < audioData.length; i++) {
                    audioArray[i] = audioData.charCodeAt(i);
                }
                
                this.sentenceAudioBuffers.get(sentenceIndex).push(audioArray);
                
                this.audioChunks++;
                document.getElementById('audioChunks').textContent = this.audioChunks;
                
                this.addMessage('received', `🎵 句子音频分片 [${sentenceIndex}] ${message.sequenceNumber}/${message.totalChunks || '?'} (${audioArray.length} bytes)`);
            }

            playSentenceAudio(sentenceIndex) {
                const chunks = this.sentenceAudioBuffers.get(sentenceIndex);
                if (!chunks || chunks.length === 0) return;
                
                // 合并音频块
                const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
                const mergedArray = new Uint8Array(totalLength);
                let offset = 0;
                
                for (const chunk of chunks) {
                    mergedArray.set(chunk, offset);
                    offset += chunk.length;
                }
                
                // 创建Blob并加入播放队列
                const audioBlob = new Blob([mergedArray], { type: 'audio/mpeg' });
                this.audioPlayQueue.push({
                    blob: audioBlob,
                    sentenceIndex: sentenceIndex
                });
                
                // 如果当前没有播放，立即开始播放
                if (!this.isPlaying) {
                    this.playNextInQueue();
                }
                
                // 清理已使用的缓冲区
                this.sentenceAudioBuffers.delete(sentenceIndex);
            }

            playNextInQueue() {
                if (this.audioPlayQueue.length === 0) {
                    this.isPlaying = false;
                    this.updateAudioStatus('播放队列为空');
                    return;
                }
                
                this.isPlaying = true;
                const audioItem = this.audioPlayQueue.shift();
                
                this.updateAudioStatus(`播放句子 [${audioItem.sentenceIndex}] (${(audioItem.blob.size / 1024).toFixed(2)} KB)`);
                
                const audioPlayer = document.getElementById('audioPlayer');
                const audioUrl = URL.createObjectURL(audioItem.blob);
                
                audioPlayer.src = audioUrl;
                audioPlayer.load();
                
                audioPlayer.addEventListener('loadeddata', () => {
                    audioPlayer.play().catch(e => {
                        this.updateAudioStatus(`播放失败: ${e.message}`);
                        this.playNextInQueue(); // 继续播放下一个
                    });
                }, { once: true });
                
                audioPlayer.addEventListener('ended', () => {
                    URL.revokeObjectURL(audioUrl);
                    this.updateAudioStatus(`句子 [${audioItem.sentenceIndex}] 播放完成`);
                    
                    // 播放下一个
                    setTimeout(() => this.playNextInQueue(), 100);
                }, { once: true });
            }

            updateVoiceLatencyDisplay() {
                if (this.voiceResponseHistory.length > 0) {
                    const latest = this.voiceResponseHistory[this.voiceResponseHistory.length - 1];

                    // 计算平均延迟
                    const avgTextDelay = this.voiceResponseHistory
                        .filter(item => item.textDelay)
                        .reduce((sum, item) => sum + item.textDelay, 0) /
                        this.voiceResponseHistory.filter(item => item.textDelay).length;

                    const avgAudioDelay = this.voiceResponseHistory
                        .filter(item => item.audioDelay)
                        .reduce((sum, item) => sum + item.audioDelay, 0) /
                        this.voiceResponseHistory.filter(item => item.audioDelay).length;

                    // 更新显示
                    const voiceLatencyElement = document.getElementById('voiceLatency');
                    if (voiceLatencyElement) {
                        voiceLatencyElement.textContent = `文本: ${latest.textDelay || '-'}ms / 音频: ${latest.audioDelay || '-'}ms`;
                    }

                    const avgVoiceLatencyElement = document.getElementById('avgVoiceLatency');
                    if (avgVoiceLatencyElement) {
                        avgVoiceLatencyElement.textContent = `文本: ${Math.round(avgTextDelay) || '-'}ms / 音频: ${Math.round(avgAudioDelay) || '-'}ms`;
                    }
                }
            }

            // 新增方法：请求性能报告
            requestPerformanceReport() {
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    this.ws.send(JSON.stringify({
                        type: 'performance_request',
                        timestamp: Date.now()
                    }));
                    this.addMessage('sent', '📊 请求性能报告');
                } else {
                    alert('WebSocket未连接');
                }
            }

            // 处理性能报告
            handlePerformanceReport(message) {
                const { performance, connection, flowControl } = message;

                this.addMessage('received', '📊 收到性能报告');

                // 更新性能数据显示
                const performanceArea = document.getElementById('performanceData');
                performanceArea.innerHTML = `
                    <div style="font-family: monospace; font-size: 12px;">
                        <h4>🔗 连接统计</h4>
                        <p>活跃连接: ${connection.activeConnections}</p>
                        <p>总连接数: ${connection.totalConnections}</p>
                        <p>平均延迟: ${connection.averageLatency}ms</p>
                        <p>数据传输: ${(connection.bytesTransferred / 1024 / 1024).toFixed(2)} MB</p>

                        <h4>⚡ 性能指标</h4>
                        <p>AI平均延迟: ${performance.summary.averageLatency.ai}ms</p>
                        <p>TTS平均延迟: ${performance.summary.averageLatency.tts}ms</p>
                        <p>音频平均延迟: ${performance.summary.averageLatency.audio}ms</p>
                        <p>错误率: ${(performance.summary.errorRate * 100).toFixed(2)}%</p>
                        <p>内存使用: ${(performance.summary.memoryUsage * 100).toFixed(1)}%</p>

                        ${flowControl ? `
                        <h4>🌊 流控制</h4>
                        <p>窗口大小: ${flowControl.windowSize} bytes</p>
                        <p>传输中数据: ${flowControl.bytesInFlight} bytes</p>
                        <p>RTT: ${flowControl.rtt}ms</p>
                        <p>自适应分片: ${flowControl.adaptiveChunkSize} bytes</p>
                        ` : ''}

                        <p style="margin-top: 10px; color: #6c757d;">
                            更新时间: ${new Date(message.timestamp).toLocaleTimeString()}
                        </p>
                    </div>
                `;

                // 更新指标卡片
                if (flowControl) {
                    document.getElementById('flowControlWindow').textContent = `${Math.round(flowControl.windowSize / 1024)}KB`;
                }
            }

            // 处理流控制确认
            handleFlowControlAck(message) {
                // 这里可以添加流控制相关的UI更新
                console.log('Flow control ack received:', message);
            }

            // 导出性能数据
            exportPerformanceData() {
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    // 先请求最新的性能报告
                    this.requestPerformanceReport();

                    // 延迟一下再导出，确保获取到最新数据
                    setTimeout(() => {
                        const performanceArea = document.getElementById('performanceData');
                        const data = performanceArea.textContent || performanceArea.innerText;

                        const blob = new Blob([data], { type: 'text/plain' });
                        const url = URL.createObjectURL(blob);

                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `performance_report_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);

                        URL.revokeObjectURL(url);
                        this.addMessage('system', '📁 性能数据已导出');
                    }, 1000);
                } else {
                    alert('WebSocket未连接');
                }
            }
        }

        // 初始化测试器
        document.addEventListener('DOMContentLoaded', () => {
            new WebSocketTester();
        });
    </script>
</body>
</html> 